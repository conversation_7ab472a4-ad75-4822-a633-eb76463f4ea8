package docs

import (
	"time"

	"github.com/google/uuid"
)

// API Response Models for Swagger Documentation

// ErrorResponse represents an API error response
// @Description Standard error response format
type ErrorResponse struct {
	Error   string                 `json:"error" example:"validation_failed"`
	Message string                 `json:"message" example:"Request validation failed"`
	Details map[string]interface{} `json:"details,omitempty"`
	Code    int                    `json:"code" example:"400"`
} // @name ErrorResponse

// SuccessResponse represents a successful API response
// @Description Standard success response format
type SuccessResponse struct {
	Success bool        `json:"success" example:"true"`
	Message string      `json:"message" example:"Operation completed successfully"`
	Data    interface{} `json:"data,omitempty"`
} // @name SuccessResponse

// PaginationInfo represents pagination information
// @Description Pagination metadata
type PaginationInfo struct {
	Page       int   `json:"page" example:"1"`
	PageSize   int   `json:"page_size" example:"20"`
	Total      int64 `json:"total" example:"150"`
	TotalPages int   `json:"total_pages" example:"8"`
} // @name PaginationInfo

// PaginatedResponse represents a paginated API response
// @Description Standard paginated response format
type PaginatedResponse struct {
	Success    bool           `json:"success" example:"true"`
	Data       interface{}    `json:"data"`
	Pagination PaginationInfo `json:"pagination"`
} // @name PaginatedResponse

// License Models

// PaginatedLicenseResponse represents a paginated list of licenses
// @Description Paginated license response
type PaginatedLicenseResponse struct {
	Success    bool              `json:"success" example:"true"`
	Data       []LicenseResponse `json:"data"`
	Pagination PaginationInfo    `json:"pagination"`
} // @name PaginatedLicenseResponse

// PaginatedOrganizationResponse represents a paginated list of organizations
// @Description Paginated organization response
type PaginatedOrganizationResponse struct {
	Success    bool                   `json:"success" example:"true"`
	Data       []OrganizationResponse `json:"data"`
	Pagination PaginationInfo         `json:"pagination"`
} // @name PaginatedOrganizationResponse

// PaginatedUserResponse represents a paginated list of users
// @Description Paginated user response
type PaginatedUserResponse struct {
	Success    bool           `json:"success" example:"true"`
	Data       []UserResponse `json:"data"`
	Pagination PaginationInfo `json:"pagination"`
} // @name PaginatedUserResponse

// PaginatedProductResponse represents a paginated list of products
// @Description Paginated product response
type PaginatedProductResponse struct {
	Success    bool              `json:"success" example:"true"`
	Data       []ProductResponse `json:"data"`
	Pagination PaginationInfo    `json:"pagination"`
} // @name PaginatedProductResponse

// PaginatedPolicyResponse represents a paginated list of policies
// @Description Paginated policy response
type PaginatedPolicyResponse struct {
	Success    bool             `json:"success" example:"true"`
	Data       []PolicyResponse `json:"data"`
	Pagination PaginationInfo   `json:"pagination"`
} // @name PaginatedPolicyResponse

// PaginatedMachineResponse represents a paginated list of machines
// @Description Paginated machine response
type PaginatedMachineResponse struct {
	Success    bool              `json:"success" example:"true"`
	Data       []MachineResponse `json:"data"`
	Pagination PaginationInfo    `json:"pagination"`
} // @name PaginatedMachineResponse

// LicenseResponse represents a license in API responses
// @Description License information
type LicenseResponse struct {
	ID              uuid.UUID  `json:"id" example:"550e8400-e29b-41d4-a716-************"`
	Key             string     `json:"key" example:"GLK-1234-5678-ABCD-EFGH"`
	OrganizationID  uuid.UUID  `json:"organization_id" example:"550e8400-e29b-41d4-a716-************"`
	ProductID       uuid.UUID  `json:"product_id" example:"550e8400-e29b-41d4-a716-************"`
	PolicyID        *uuid.UUID `json:"policy_id,omitempty" example:"550e8400-e29b-41d4-a716-************"`
	UserID          *uuid.UUID `json:"user_id,omitempty" example:"550e8400-e29b-41d4-a716-************"`
	Name            *string    `json:"name,omitempty" example:"Enterprise License"`
	Status          string     `json:"status" example:"active"`
	Uses            int        `json:"uses" example:"42"`
	Protected       *bool      `json:"protected,omitempty" example:"false"`
	Suspended       *bool      `json:"suspended,omitempty" example:"false"`
	ExpiresAt       *time.Time `json:"expires_at,omitempty" example:"2025-12-31T23:59:59Z"`
	LastValidated   *time.Time `json:"last_validated,omitempty" example:"2024-01-15T10:30:00Z"`
	ValidationCount int        `json:"validation_count" example:"1337"`
	CreatedAt       time.Time  `json:"created_at" example:"2024-01-01T00:00:00Z"`
	UpdatedAt       time.Time  `json:"updated_at" example:"2024-01-15T10:30:00Z"`
} // @name LicenseResponse

// LicenseValidationRequest represents a license validation request
// @Description License validation request payload
type LicenseValidationRequest struct {
	LicenseKey         string            `json:"license_key" binding:"required" example:"GLK-1234-5678-ABCD-EFGH"`
	MachineFingerprint *string           `json:"machine_fingerprint,omitempty" example:"fp-machine-12345"`
	MachineMetadata    map[string]string `json:"machine_metadata,omitempty"`
} // @name LicenseValidationRequest

// LicenseValidationResponse represents a license validation response
// @Description License validation result
type LicenseValidationResponse struct {
	Valid           bool                  `json:"valid" example:"true"`
	License         *LicenseResponse      `json:"license,omitempty"`
	Organization    *OrganizationResponse `json:"organization,omitempty"`
	Policy          *PolicyResponse       `json:"policy,omitempty"`
	MachinesUsed    int                   `json:"machines_used" example:"3"`
	MachinesAllowed int                   `json:"machines_allowed" example:"5"`
	ExpiresAt       *time.Time            `json:"expires_at,omitempty" example:"2025-12-31T23:59:59Z"`
	Errors          []string              `json:"errors,omitempty"`
	TTL             int                   `json:"ttl" example:"3600"`
} // @name LicenseValidationResponse

// Organization Models

// OrganizationResponse represents an organization in API responses
// @Description Organization information
type OrganizationResponse struct {
	ID           uuid.UUID `json:"id" example:"550e8400-e29b-41d4-a716-************"`
	Name         string    `json:"name" example:"Enterprise Corp"`
	Slug         string    `json:"slug" example:"enterprise-corp"`
	Email        *string   `json:"email,omitempty" example:"<EMAIL>"`
	Protected    *bool     `json:"protected,omitempty" example:"false"`
	Suspended    *bool     `json:"suspended,omitempty" example:"false"`
	BillingEmail *string   `json:"billing_email,omitempty" example:"<EMAIL>"`
	CreatedAt    time.Time `json:"created_at" example:"2024-01-01T00:00:00Z"`
	UpdatedAt    time.Time `json:"updated_at" example:"2024-01-15T10:30:00Z"`
} // @name OrganizationResponse

// Policy Models

// PolicyResponse represents a policy in API responses
// @Description Policy configuration
type PolicyResponse struct {
	ID                uuid.UUID  `json:"id" example:"550e8400-e29b-41d4-a716-************"`
	OrganizationID    uuid.UUID  `json:"organization_id" example:"550e8400-e29b-41d4-a716-************"`
	ProductID         *uuid.UUID `json:"product_id,omitempty" example:"550e8400-e29b-41d4-a716-************"`
	Name              string     `json:"name" example:"Standard Policy"`
	Duration          *string    `json:"duration,omitempty" example:"P1Y"`
	Scheme            string     `json:"scheme" example:"RSA-2048"`
	RequireHeartbeat  *bool      `json:"require_heartbeat,omitempty" example:"true"`
	HeartbeatDuration *string    `json:"heartbeat_duration,omitempty" example:"PT1H"`
	MaxMachines       *int       `json:"max_machines,omitempty" example:"5"`
	MaxProcesses      *int       `json:"max_processes,omitempty" example:"10"`
	MaxUsers          *int       `json:"max_users,omitempty" example:"100"`
	MaxUses           *int       `json:"max_uses,omitempty" example:"1000"`
	Protected         *bool      `json:"protected,omitempty" example:"false"`
	Strict            *bool      `json:"strict,omitempty" example:"true"`
	Floating          *bool      `json:"floating,omitempty" example:"false"`
	CreatedAt         time.Time  `json:"created_at" example:"2024-01-01T00:00:00Z"`
	UpdatedAt         time.Time  `json:"updated_at" example:"2024-01-15T10:30:00Z"`
} // @name PolicyResponse

// Machine Models

// MachineResponse represents a machine in API responses
// @Description Machine information
type MachineResponse struct {
	ID             uuid.UUID              `json:"id" example:"550e8400-e29b-41d4-a716-************"`
	OrganizationID uuid.UUID              `json:"organization_id" example:"550e8400-e29b-41d4-a716-************"`
	LicenseID      *uuid.UUID             `json:"license_id,omitempty" example:"550e8400-e29b-41d4-a716-************"`
	Fingerprint    string                 `json:"fingerprint" example:"fp-machine-12345"`
	Name           *string                `json:"name,omitempty" example:"Production Server 01"`
	Hostname       *string                `json:"hostname,omitempty" example:"prod-srv-01"`
	Platform       *string                `json:"platform,omitempty" example:"linux"`
	IP             *string                `json:"ip,omitempty" example:"*************"`
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
	RequireLicense *bool                  `json:"require_license,omitempty" example:"true"`
	LastSeen       *time.Time             `json:"last_seen,omitempty" example:"2024-01-15T10:30:00Z"`
	CreatedAt      time.Time              `json:"created_at" example:"2024-01-01T00:00:00Z"`
	UpdatedAt      time.Time              `json:"updated_at" example:"2024-01-15T10:30:00Z"`
} // @name MachineResponse

// Product Models

// ProductResponse represents a product in API responses
// @Description Product information
type ProductResponse struct {
	ID                   uuid.UUID `json:"id" example:"550e8400-e29b-41d4-a716-************"`
	OrganizationID       uuid.UUID `json:"organization_id" example:"550e8400-e29b-41d4-a716-************"`
	Name                 string    `json:"name" example:"Enterprise Software"`
	URL                  *string   `json:"url,omitempty" example:"https://enterprise.com/software"`
	DistributionStrategy *string   `json:"distribution_strategy,omitempty" example:"licensed"`
	Platforms            []string  `json:"platforms,omitempty" example:"linux,windows,macos"`
	CreatedAt            time.Time `json:"created_at" example:"2024-01-01T00:00:00Z"`
	UpdatedAt            time.Time `json:"updated_at" example:"2024-01-15T10:30:00Z"`
} // @name ProductResponse

// User Models

// UserResponse represents a user in API responses
// @Description User information
type UserResponse struct {
	ID              uuid.UUID  `json:"id" example:"550e8400-e29b-41d4-a716-************"`
	GroupID         *uuid.UUID `json:"group_id,omitempty" example:"550e8400-e29b-41d4-a716-************"`
	Email           string     `json:"email" example:"<EMAIL>"`
	FirstName       *string    `json:"first_name,omitempty" example:"John"`
	LastName        *string    `json:"last_name,omitempty" example:"Doe"`
	Role            string     `json:"role" example:"admin"`
	PasswordHash    string     `json:"-"` // Never expose password hash
	EmailVerified   *bool      `json:"email_verified,omitempty" example:"true"`
	EmailVerifiedAt *time.Time `json:"email_verified_at,omitempty" example:"2024-01-01T12:00:00Z"`
	Banned          *bool      `json:"banned,omitempty" example:"false"`
	BannedAt        *time.Time `json:"banned_at,omitempty"`
	BannedReason    *string    `json:"banned_reason,omitempty"`
	LastLoginAt     *time.Time `json:"last_login_at,omitempty" example:"2024-01-15T10:30:00Z"`
	CreatedAt       time.Time  `json:"created_at" example:"2024-01-01T00:00:00Z"`
	UpdatedAt       time.Time  `json:"updated_at" example:"2024-01-15T10:30:00Z"`
} // @name UserResponse

// Health Models

// HealthResponse represents the health check response
// @Description System health information
type HealthResponse struct {
	Status    string                 `json:"status" example:"healthy"`
	Timestamp time.Time              `json:"timestamp" example:"2024-01-15T10:30:00Z"`
	Version   string                 `json:"version" example:"1.0.0"`
	Services  map[string]interface{} `json:"services"`
	Uptime    string                 `json:"uptime" example:"72h30m15s"`
} // @name HealthResponse

// Authentication Models

// LoginRequest represents a login request
// @Description User login credentials
type LoginRequest struct {
	Email    string  `json:"email" binding:"required,email" example:"<EMAIL>"`
	Password string  `json:"password" binding:"required" example:"secretpassword"`
	GroupID  *string `json:"group_id,omitempty" example:"550e8400-e29b-41d4-a716-************"`
} // @name LoginRequest

// LoginResponse represents a login response
// @Description Login result with JWT token
type LoginResponse struct {
	Success      bool         `json:"success" example:"true"`
	Token        string       `json:"token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`
	RefreshToken string       `json:"refresh_token" example:"refresh_token_here"`
	ExpiresAt    time.Time    `json:"expires_at" example:"2024-01-16T10:30:00Z"`
	User         UserResponse `json:"user"`
} // @name LoginResponse

// RegisterRequest represents a user registration request
// @Description User registration details
type RegisterRequest struct {
	Email               string  `json:"email" binding:"required,email" example:"<EMAIL>"`
	Password            string  `json:"password" binding:"required" example:"SecurePassword123!"`
	FirstName           *string `json:"first_name,omitempty" example:"John"`
	LastName            *string `json:"last_name,omitempty" example:"Doe"`
	GroupID             *string `json:"group_id,omitempty" example:"550e8400-e29b-41d4-a716-************"`
	CreatePersonalGroup *bool   `json:"create_personal_group,omitempty" example:"true"`
} // @name RegisterRequest

// RegisterResponse represents a successful registration response
// @Description Registration response with JWT token and user information
type RegisterResponse struct {
	Success      bool                  `json:"success" example:"true"`
	Token        string                `json:"token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`
	RefreshToken string                `json:"refresh_token" example:"refresh_token_here"`
	ExpiresAt    time.Time             `json:"expires_at" example:"2024-01-16T10:30:00Z"`
	User         UserResponse          `json:"user"`
	Organization *OrganizationResponse `json:"organization,omitempty"`
} // @name RegisterResponse

// ChangePasswordRequest represents a password change request
// @Description Password change details
type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password" binding:"required" example:"OldPassword123!"`
	NewPassword     string `json:"new_password" binding:"required" example:"NewPassword123!"`
} // @name ChangePasswordRequest

// Security Models

// APIKeyResponse represents an API key
// @Description API key information
type APIKeyResponse struct {
	ID             uuid.UUID  `json:"id" example:"550e8400-e29b-41d4-a716-************"`
	Name           string     `json:"name" example:"Production API Key"`
	Key            string     `json:"key,omitempty" example:"ak_prod_1234567890abcdef"` // Only shown on creation
	KeyHash        string     `json:"-"`                                                // Never expose key hash
	OrganizationID uuid.UUID  `json:"organization_id" example:"550e8400-e29b-41d4-a716-************"`
	UserID         *uuid.UUID `json:"user_id,omitempty" example:"550e8400-e29b-41d4-a716-************"`
	ExpiresAt      *time.Time `json:"expires_at,omitempty" example:"2025-01-15T10:30:00Z"`
	LastUsed       *time.Time `json:"last_used,omitempty" example:"2024-01-15T10:30:00Z"`
	CreatedAt      time.Time  `json:"created_at" example:"2024-01-01T00:00:00Z"`
	UpdatedAt      time.Time  `json:"updated_at" example:"2024-01-15T10:30:00Z"`
} // @name APIKeyResponse

// Metrics Models

// MetricsResponse represents system metrics
// @Description System performance metrics
type MetricsResponse struct {
	Timestamp           time.Time `json:"timestamp" example:"2024-01-15T10:30:00Z"`
	ActiveLicenses      int       `json:"active_licenses" example:"1500"`
	TotalValidations    int64     `json:"total_validations" example:"50000"`
	ValidationRate      float64   `json:"validation_rate" example:"125.5"`
	ErrorRate           float64   `json:"error_rate" example:"0.02"`
	AverageResponseTime float64   `json:"avg_response_time_ms" example:"45.2"`
	CacheHitRate        float64   `json:"cache_hit_rate" example:"0.95"`
} // @name MetricsResponse

// Additional Request/Response Models for API Routes

// Organization Models

// CreateOrganizationRequest represents organization creation request
// @Description Organization creation details
type CreateOrganizationRequest struct {
	Name        string                 `json:"name" binding:"required" example:"Enterprise Corp"`
	Slug        string                 `json:"slug" binding:"required" example:"enterprise-corp"`
	Description *string                `json:"description,omitempty" example:"Enterprise software company"`
	Website     *string                `json:"website,omitempty" example:"https://enterprise.com"`
	Settings    map[string]interface{} `json:"settings,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
} // @name CreateOrganizationRequest

// UpdateOrganizationRequest represents organization update request
// @Description Organization update details
type UpdateOrganizationRequest struct {
	Name        *string                `json:"name,omitempty" example:"Enterprise Corp Updated"`
	Description *string                `json:"description,omitempty" example:"Updated description"`
	Website     *string                `json:"website,omitempty" example:"https://enterprise-updated.com"`
	Settings    map[string]interface{} `json:"settings,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
} // @name UpdateOrganizationRequest

// SystemStatsResponse represents system-wide statistics
// @Description Comprehensive system statistics
type SystemStatsResponse struct {
	Organizations struct {
		Total  int `json:"total" example:"25"`
		Active int `json:"active" example:"23"`
	} `json:"organizations"`
	Users struct {
		Total  int `json:"total" example:"150"`
		Active int `json:"active" example:"142"`
	} `json:"users"`
	Licenses struct {
		Total     int `json:"total" example:"1500"`
		Active    int `json:"active" example:"1350"`
		Suspended int `json:"suspended" example:"50"`
		Expired   int `json:"expired" example:"100"`
	} `json:"licenses"`
	Machines struct {
		Total  int `json:"total" example:"800"`
		Active int `json:"active" example:"750"`
	} `json:"machines"`
	Usage struct {
		ValidationsToday int64   `json:"validations_today" example:"5000"`
		ValidationRate   float64 `json:"validation_rate_per_hour" example:"208.3"`
		ErrorRate        float64 `json:"error_rate" example:"0.02"`
	} `json:"usage"`
} // @name SystemStatsResponse

// User Models

// OrganizationUserResponse represents a user in organization context
// @Description User information within organization context
type OrganizationUserResponse struct {
	User      UserResponse `json:"user"`
	JoinedAt  time.Time    `json:"joined_at" example:"2024-01-01T00:00:00Z"`
	InvitedBy *string      `json:"invited_by,omitempty" example:"550e8400-e29b-41d4-a716-************"`
	Role      string       `json:"role" example:"admin"`
} // @name OrganizationUserResponse

// AddUserToOrganizationRequest represents request to add user to organization
// @Description Request to add user to organization
type AddUserToOrganizationRequest struct {
	Email     *string `json:"email,omitempty" example:"<EMAIL>"`
	UserID    *string `json:"user_id,omitempty" example:"550e8400-e29b-41d4-a716-************"`
	Role      string  `json:"role" binding:"required" example:"member"`
	SendEmail *bool   `json:"send_email,omitempty" example:"true"`
} // @name AddUserToOrganizationRequest

// UserOrganizationResponse represents user-organization relationship
// @Description User organization membership details
type UserOrganizationResponse struct {
	Organization OrganizationResponse `json:"organization"`
	Role         string               `json:"role" example:"admin"`
	JoinedAt     time.Time            `json:"joined_at" example:"2024-01-01T00:00:00Z"`
	Permissions  []string             `json:"permissions" example:"[\"license.read\", \"machine.read\"]"`
} // @name UserOrganizationResponse

// UserPermissionsResponse represents user permissions
// @Description User permissions across organizations
type UserPermissionsResponse struct {
	GlobalPermissions []string                           `json:"global_permissions" example:"[\"system.admin\"]"`
	Organizations     map[string]OrganizationPermissions `json:"organizations"`
} // @name UserPermissionsResponse

// OrganizationPermissions represents permissions within an organization
// @Description Permissions within a specific organization
type OrganizationPermissions struct {
	Role        string   `json:"role" example:"admin"`
	Permissions []string `json:"permissions" example:"[\"license.read\", \"license.write\", \"machine.read\"]"`
} // @name OrganizationPermissions

// GrantPermissionRequest represents permission grant request
// @Description Request to grant permission to user
type GrantPermissionRequest struct {
	Permission   string  `json:"permission" binding:"required" example:"license.write"`
	ResourceType *string `json:"resource_type,omitempty" example:"license"`
	ResourceID   *string `json:"resource_id,omitempty" example:"550e8400-e29b-41d4-a716-************"`
} // @name GrantPermissionRequest

// RevokePermissionRequest represents permission revocation request
// @Description Request to revoke permission from user
type RevokePermissionRequest struct {
	Permission   string  `json:"permission" binding:"required" example:"license.write"`
	ResourceType *string `json:"resource_type,omitempty" example:"license"`
	ResourceID   *string `json:"resource_id,omitempty" example:"550e8400-e29b-41d4-a716-************"`
} // @name RevokePermissionRequest

// UpdateUserRequest represents user profile update request
// @Description User profile update details
type UpdateUserRequest struct {
	FirstName *string `json:"first_name,omitempty" example:"John"`
	LastName  *string `json:"last_name,omitempty" example:"Doe"`
	Email     *string `json:"email,omitempty" example:"<EMAIL>"`
} // @name UpdateUserRequest

// LogoutResponse represents logout response
// @Description Logout confirmation
type LogoutResponse struct {
	Success bool   `json:"success" example:"true"`
	Message string `json:"message" example:"Successfully logged out"`
} // @name LogoutResponse

// RefreshTokenResponse represents token refresh response
// @Description New JWT token after refresh
type RefreshTokenResponse struct {
	Success   bool      `json:"success" example:"true"`
	Token     string    `json:"token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`
	ExpiresAt time.Time `json:"expires_at" example:"2024-01-16T10:30:00Z"`
} // @name RefreshTokenResponse

// License Models

// LicenseStatsResponse represents license statistics
// @Description Comprehensive license statistics and metrics
type LicenseStatsResponse struct {
	Usage struct {
		Current int `json:"current" example:"150"`
		Limit   int `json:"limit" example:"500"`
	} `json:"usage"`
	Machines struct {
		Active int `json:"active" example:"25"`
		Limit  int `json:"limit" example:"100"`
	} `json:"machines"`
	Validations struct {
		Today     int64   `json:"today" example:"500"`
		ThisWeek  int64   `json:"this_week" example:"3500"`
		ThisMonth int64   `json:"this_month" example:"15000"`
		Total     int64   `json:"total" example:"150000"`
		ErrorRate float64 `json:"error_rate" example:"0.02"`
	} `json:"validations"`
	LastValidation *time.Time `json:"last_validation,omitempty" example:"2024-01-15T10:30:00Z"`
	CreatedAt      time.Time  `json:"created_at" example:"2024-01-01T00:00:00Z"`
	ExpiresAt      *time.Time `json:"expires_at,omitempty" example:"2025-01-01T00:00:00Z"`
} // @name LicenseStatsResponse

// SuspendLicenseRequest represents license suspension request
// @Description License suspension details
type SuspendLicenseRequest struct {
	Reason   *string                `json:"reason,omitempty" example:"Payment overdue"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
} // @name SuspendLicenseRequest

// RevokeLicenseRequest represents license revocation request
// @Description License revocation details
type RevokeLicenseRequest struct {
	Reason   *string                `json:"reason,omitempty" example:"Terms violation"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
} // @name RevokeLicenseRequest

// RenewLicenseRequest represents license renewal request
// @Description License renewal details
type RenewLicenseRequest struct {
	ExpiresAt *time.Time             `json:"expires_at,omitempty" example:"2025-01-01T00:00:00Z"`
	Duration  *string                `json:"duration,omitempty" example:"1y"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
} // @name RenewLicenseRequest

// CheckInLicenseRequest represents license check-in request
// @Description License check-in details
type CheckInLicenseRequest struct {
	MachineID *string                `json:"machine_id,omitempty" example:"550e8400-e29b-41d4-a716-************"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
} // @name CheckInLicenseRequest

// IncrementUsageRequest represents usage increment request
// @Description Usage increment details
type IncrementUsageRequest struct {
	Amount   int                    `json:"amount" example:"1"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
} // @name IncrementUsageRequest

// DecrementUsageRequest represents usage decrement request
// @Description Usage decrement details
type DecrementUsageRequest struct {
	Amount   int                    `json:"amount" example:"1"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
} // @name DecrementUsageRequest

// LicenseUsageResponse represents license usage response
// @Description License usage information
type LicenseUsageResponse struct {
	Current   int       `json:"current" example:"150"`
	Limit     int       `json:"limit" example:"500"`
	UpdatedAt time.Time `json:"updated_at" example:"2024-01-15T10:30:00Z"`
} // @name LicenseUsageResponse

// CheckoutLicenseRequest represents license checkout request
// @Description License checkout details with machine information
type CheckoutLicenseRequest struct {
	Machine  CreateMachineRequest   `json:"machine" binding:"required"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
} // @name CheckoutLicenseRequest

// LicenseCheckoutResponse represents license checkout response
// @Description License checkout result with machine details
type LicenseCheckoutResponse struct {
	License      LicenseResponse        `json:"license"`
	Machine      MachineResponse        `json:"machine"`
	CheckedOut   bool                   `json:"checked_out" example:"true"`
	CheckedOutAt time.Time              `json:"checked_out_at" example:"2024-01-15T10:30:00Z"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
} // @name LicenseCheckoutResponse

// LicenseCheckoutStatusResponse represents license checkout status
// @Description License checkout status and machine information
type LicenseCheckoutStatusResponse struct {
	License        LicenseResponse   `json:"license"`
	CheckedOut     bool              `json:"checked_out" example:"true"`
	Machines       []MachineResponse `json:"machines"`
	AvailableSlots int               `json:"available_slots" example:"75"`
} // @name LicenseCheckoutStatusResponse

// Machine Models

// CreateMachineRequest represents machine creation request
// @Description Machine creation details with fingerprint and system information
type CreateMachineRequest struct {
	Name        string                 `json:"name" binding:"required" example:"Production Server 01"`
	Fingerprint string                 `json:"fingerprint" binding:"required" example:"machine-fingerprint-hash"`
	Platform    string                 `json:"platform" example:"linux"`
	Hostname    *string                `json:"hostname,omitempty" example:"prod-server-01"`
	IPAddress   *string                `json:"ip_address,omitempty" example:"*************"`
	Cores       *int                   `json:"cores,omitempty" example:"8"`
	Memory      *int64                 `json:"memory,omitempty" example:"16777216"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
} // @name CreateMachineRequest

// UpdateMachineRequest represents machine update request
// @Description Machine update details
type UpdateMachineRequest struct {
	Name      *string                `json:"name,omitempty" example:"Updated Server Name"`
	Platform  *string                `json:"platform,omitempty" example:"linux"`
	Hostname  *string                `json:"hostname,omitempty" example:"updated-hostname"`
	IPAddress *string                `json:"ip_address,omitempty" example:"*************"`
	Cores     *int                   `json:"cores,omitempty" example:"16"`
	Memory    *int64                 `json:"memory,omitempty" example:"33554432"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
} // @name UpdateMachineRequest

// MachineHeartbeatRequest represents machine heartbeat request
// @Description Heartbeat details with optional system metrics
type MachineHeartbeatRequest struct {
	CPUUsage    *float64               `json:"cpu_usage,omitempty" example:"45.2"`
	MemoryUsage *float64               `json:"memory_usage,omitempty" example:"68.5"`
	DiskUsage   *float64               `json:"disk_usage,omitempty" example:"32.1"`
	Uptime      *int64                 `json:"uptime,omitempty" example:"86400"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
} // @name MachineHeartbeatRequest

// MachineHeartbeatResponse represents machine heartbeat response
// @Description Heartbeat response with updated machine status
type MachineHeartbeatResponse struct {
	Machine    MachineResponse `json:"machine"`
	Recorded   bool            `json:"recorded" example:"true"`
	RecordedAt time.Time       `json:"recorded_at" example:"2024-01-15T10:30:00Z"`
	NextDue    *time.Time      `json:"next_due,omitempty" example:"2024-01-15T11:30:00Z"`
	Status     string          `json:"status" example:"active"`
} // @name MachineHeartbeatResponse

// MachineProcessesResponse represents machine processes response
// @Description List of processes running on the machine
type MachineProcessesResponse struct {
	Processes []ProcessInfo `json:"processes"`
	Count     int           `json:"count" example:"25"`
	UpdatedAt time.Time     `json:"updated_at" example:"2024-01-15T10:30:00Z"`
} // @name MachineProcessesResponse

// ProcessInfo represents process information
// @Description Information about a running process
type ProcessInfo struct {
	PID        int       `json:"pid" example:"1234"`
	Name       string    `json:"name" example:"myapp"`
	Command    string    `json:"command" example:"/usr/bin/myapp --config=/etc/myapp.conf"`
	CPUPercent float64   `json:"cpu_percent" example:"2.5"`
	MemoryMB   int64     `json:"memory_mb" example:"128"`
	Status     string    `json:"status" example:"running"`
	StartedAt  time.Time `json:"started_at" example:"2024-01-15T09:00:00Z"`
} // @name ProcessInfo

// MachineComponentsResponse represents machine components response
// @Description Machine hardware and software components information
type MachineComponentsResponse struct {
	Hardware  HardwareInfo `json:"hardware"`
	Software  SoftwareInfo `json:"software"`
	UpdatedAt time.Time    `json:"updated_at" example:"2024-01-15T10:30:00Z"`
} // @name MachineComponentsResponse

// HardwareInfo represents hardware information
// @Description Hardware component details
type HardwareInfo struct {
	CPU    CPUInfo    `json:"cpu"`
	Memory MemoryInfo `json:"memory"`
	Disks  []DiskInfo `json:"disks"`
} // @name HardwareInfo

// CPUInfo represents CPU information
// @Description CPU details
type CPUInfo struct {
	Model     string  `json:"model" example:"Intel(R) Core(TM) i7-9700K CPU @ 3.60GHz"`
	Cores     int     `json:"cores" example:"8"`
	Threads   int     `json:"threads" example:"8"`
	Frequency float64 `json:"frequency_ghz" example:"3.6"`
} // @name CPUInfo

// MemoryInfo represents memory information
// @Description Memory details
type MemoryInfo struct {
	Total     int64 `json:"total_mb" example:"16384"`
	Available int64 `json:"available_mb" example:"8192"`
	Used      int64 `json:"used_mb" example:"8192"`
} // @name MemoryInfo

// DiskInfo represents disk information
// @Description Disk details
type DiskInfo struct {
	Device     string `json:"device" example:"/dev/sda1"`
	MountPoint string `json:"mount_point" example:"/"`
	FileSystem string `json:"filesystem" example:"ext4"`
	Total      int64  `json:"total_gb" example:"500"`
	Used       int64  `json:"used_gb" example:"150"`
	Available  int64  `json:"available_gb" example:"350"`
} // @name DiskInfo

// SoftwareInfo represents software information
// @Description Software component details
type SoftwareInfo struct {
	OS           OSInfo            `json:"os"`
	Runtime      *RuntimeInfo      `json:"runtime,omitempty"`
	Dependencies []DependencyInfo  `json:"dependencies,omitempty"`
	Environment  map[string]string `json:"environment,omitempty"`
} // @name SoftwareInfo

// OSInfo represents operating system information
// @Description Operating system details
type OSInfo struct {
	Name    string `json:"name" example:"Ubuntu"`
	Version string `json:"version" example:"20.04.3 LTS"`
	Arch    string `json:"arch" example:"x86_64"`
	Kernel  string `json:"kernel" example:"5.4.0-91-generic"`
} // @name OSInfo

// RuntimeInfo represents runtime information
// @Description Runtime environment details
type RuntimeInfo struct {
	Name    string `json:"name" example:"Go"`
	Version string `json:"version" example:"1.19.5"`
	Path    string `json:"path" example:"/usr/local/go/bin/go"`
} // @name RuntimeInfo

// DependencyInfo represents dependency information
// @Description Software dependency details
type DependencyInfo struct {
	Name    string `json:"name" example:"gin"`
	Version string `json:"version" example:"v1.9.1"`
	Type    string `json:"type" example:"module"`
} // @name DependencyInfo

// Additional Request Models

// LicenseValidationByKeyRequest represents license validation by key request
// @Description License validation request with key and optional scope
type LicenseValidationByKeyRequest struct {
	Key   string `json:"key" binding:"required" example:"GLK-1234-5678-ABCD-EFGH"`
	Scope *struct {
		Fingerprint *string                `json:"fingerprint,omitempty" example:"machine-fingerprint-abc123"`
		Metadata    map[string]interface{} `json:"metadata,omitempty"`
	} `json:"scope,omitempty"`
} // @name LicenseValidationByKeyRequest

// CreateLicenseRequest represents license creation request
// @Description License creation details
type CreateLicenseRequest struct {
	PolicyID    string                 `json:"policy_id" binding:"required" example:"550e8400-e29b-41d4-a716-************"`
	Name        *string                `json:"name,omitempty" example:"Enterprise License"`
	Email       *string                `json:"email,omitempty" example:"<EMAIL>"`
	ExpiresAt   *time.Time             `json:"expires_at,omitempty" example:"2025-01-15T10:30:00Z"`
	MaxMachines *int                   `json:"max_machines,omitempty" example:"10"`
	MaxUsage    *int                   `json:"max_usage,omitempty" example:"1000"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	Protected   *bool                  `json:"protected,omitempty" example:"false"`
} // @name CreateLicenseRequest

// UpdateLicenseRequest represents license update request
// @Description License update details
type UpdateLicenseRequest struct {
	Name        *string                `json:"name,omitempty" example:"Updated Enterprise License"`
	Email       *string                `json:"email,omitempty" example:"<EMAIL>"`
	ExpiresAt   *time.Time             `json:"expires_at,omitempty" example:"2025-12-31T23:59:59Z"`
	MaxMachines *int                   `json:"max_machines,omitempty" example:"20"`
	MaxUsage    *int                   `json:"max_usage,omitempty" example:"2000"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	Protected   *bool                  `json:"protected,omitempty" example:"true"`
} // @name UpdateLicenseRequest

// LicenseActionRequest represents license action request
// @Description License action details (suspend, reinstate, revoke)
type LicenseActionRequest struct {
	Reason   *string                `json:"reason,omitempty" example:"Policy violation"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
} // @name LicenseActionRequest

// UsageActionRequest represents usage action request
// @Description Usage increment/decrement details
type UsageActionRequest struct {
	Increment *int                   `json:"increment,omitempty" example:"5"`
	Decrement *int                   `json:"decrement,omitempty" example:"3"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
} // @name UsageActionRequest

// CreatePolicyRequest represents policy creation request
// @Description Policy creation details
type CreatePolicyRequest struct {
	Name                      string                 `json:"name" binding:"required" example:"Enterprise Policy"`
	Duration                  *int64                 `json:"duration,omitempty" example:"31536000"`
	Strict                    *bool                  `json:"strict,omitempty" example:"true"`
	Floating                  *bool                  `json:"floating,omitempty" example:"false"`
	RequireHeartbeat          *bool                  `json:"require_heartbeat,omitempty" example:"true"`
	HeartbeatDuration         *int64                 `json:"heartbeat_duration,omitempty" example:"3600"`
	MachineUniquenessStrategy *string                `json:"machine_uniqueness_strategy,omitempty" example:"unique_per_license"`
	MaxMachines               *int                   `json:"max_machines,omitempty" example:"10"`
	MaxProcesses              *int                   `json:"max_processes,omitempty" example:"5"`
	MaxCores                  *int                   `json:"max_cores,omitempty" example:"8"`
	MaxUses                   *int                   `json:"max_uses,omitempty" example:"1000"`
	Metadata                  map[string]interface{} `json:"metadata,omitempty"`
} // @name CreatePolicyRequest

// CreateProductRequest represents product creation request
// @Description Product creation details
type CreateProductRequest struct {
	Name                 string                 `json:"name" binding:"required" example:"Enterprise Software"`
	Code                 string                 `json:"code,omitempty" example:"enterprise-software"`
	Description          *string                `json:"description,omitempty" example:"Comprehensive enterprise software solution"`
	URL                  *string                `json:"url,omitempty" example:"https://enterprise.com/software"`
	DistributionStrategy *string                `json:"distribution_strategy,omitempty" example:"licensed"`
	Platforms            []string               `json:"platforms,omitempty" example:"linux,windows,macos"`
	Metadata             map[string]interface{} `json:"metadata,omitempty"`
} // @name CreateProductRequest
