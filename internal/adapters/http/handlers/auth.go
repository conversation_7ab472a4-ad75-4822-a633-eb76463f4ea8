package handlers

import (
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	_ "github.com/gokeys/gokeys/internal/adapters/http/docs"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services/auth"
	"golang.org/x/crypto/bcrypt"
)

// AuthHandler handles authentication-related HTTP requests
type AuthHandler struct {
	userRepo         repositories.UserRepository
	sessionRepo      repositories.SessionRepository
	usersOrgRepo     repositories.UsersOrganizationRepository
	organizationRepo repositories.OrganizationRepository
	permissionRepo   repositories.PermissionRepository
	authService      *auth.AuthService
}

// NewAuthHandler creates a new authentication handler
func NewAuthHandler(
	userRepo repositories.UserRepository,
	sessionRepo repositories.SessionRepository,
	usersOrgRepo repositories.UsersOrganizationRepository,
	organizationRepo repositories.OrganizationRepository,
	permissionRepo repositories.PermissionRepository,
	authService *auth.AuthService,
) *AuthHandler {
	return &AuthHandler{
		userRepo:         userRepo,
		sessionRepo:      sessionRepo,
		usersOrgRepo:     usersOrgRepo,
		organizationRepo: organizationRepo,
		permissionRepo:   permissionRepo,
		authService:      authService,
	}
}

// Login authenticates a user and returns a JWT token
// @Summary User login
// @Description Authenticate user with email and password, returns JWT token and user information
// @Tags Authentication
// @Accept json
// @Produce json
// @Param request body docs.LoginRequest true "Login credentials"
// @Success 200 {object} docs.LoginResponse "Successful authentication with JWT token and user details"
// @Failure 400 {object} docs.ErrorResponse "Invalid request format or missing required fields"
// @Failure 401 {object} docs.ErrorResponse "Invalid credentials or account banned"
// @Failure 500 {object} docs.ErrorResponse "Internal server error"
// @Router /public/auth/login [post]
func (h *AuthHandler) Login(c *gin.Context) {
	var requestData struct {
		Email    string `json:"email" binding:"required,email"`
		Password string `json:"password" binding:"required"`
	}

	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request", "reason": err.Error()})
		return
	}

	// Find user by email
	user, err := h.userRepo.GetByEmail(c.Request.Context(), requestData.Email)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid credentials"})
		return
	}

	// Verify password
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(requestData.Password)); err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid credentials"})
		return
	}

	// Check if user is banned
	if user.BannedAt != nil {
		c.JSON(http.StatusForbidden, gin.H{"error": "account banned"})
		return
	}

	// Create session with temporary token hash (will be updated after JWT generation)
	session := &entities.Session{
		UserID:    user.ID,
		TokenHash: fmt.Sprintf("temp-%d", time.Now().UnixNano()), // Unique temporary hash
		ExpiresAt: time.Now().Add(24 * time.Hour),                // 24 hour session
	}

	if err := h.sessionRepo.Create(c.Request.Context(), session); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create session", "reason": err.Error()})
		return
	}

	// Generate JWT token
	token, err := h.authService.GenerateJWT(user.ID, session.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to generate token", "reason": err.Error()})
		return
	}

	// Update last login
	user.LastLogin = &[]time.Time{time.Now()}[0]
	h.userRepo.Update(c.Request.Context(), user)

	// Remove sensitive information from response
	user.Password = ""

	c.JSON(http.StatusOK, gin.H{
		"token":      token,
		"user":       user,
		"session_id": session.ID,
		"expires_at": session.ExpiresAt,
	})
}

// Register creates a new user account
// @Summary User registration
// @Description Register a new user account with optional organization creation
// @Tags Authentication
// @Accept json
// @Produce json
// @Param request body docs.RegisterRequest true "Registration details"
// @Success 201 {object} docs.RegisterResponse "Successful registration with JWT token and user details"
// @Failure 400 {object} docs.ErrorResponse "Invalid request format, validation errors, or email already exists"
// @Failure 500 {object} docs.ErrorResponse "Internal server error during user creation"
// @Router /public/auth/register [post]
func (h *AuthHandler) Register(c *gin.Context) {
	var requestData struct {
		Email            string  `json:"email" binding:"required,email"`
		Password         string  `json:"password" binding:"required,min=6"`
		FirstName        string  `json:"first_name" binding:"required"`
		LastName         string  `json:"last_name" binding:"required"`
		OrganizationName *string `json:"organization_name"` // Optional for B2B registration
	}

	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request", "reason": err.Error()})
		return
	}

	// Check if user already exists
	existing, err := h.userRepo.GetByEmail(c.Request.Context(), requestData.Email)
	if err == nil && existing != nil {
		c.JSON(http.StatusConflict, gin.H{"error": "user already exists"})
		return
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(requestData.Password), bcrypt.DefaultCost)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to hash password"})
		return
	}

	// Create user
	user := &entities.User{
		Email:     requestData.Email,
		Password:  string(hashedPassword),
		FirstName: &requestData.FirstName,
		LastName:  &requestData.LastName,
		Status:    entities.UserStatusActive,
	}

	if err := h.userRepo.Create(c.Request.Context(), user); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create user", "reason": err.Error()})
		return
	}

	// If organization name provided, create organization and make user admin
	var organization *entities.Organization
	if requestData.OrganizationName != nil && *requestData.OrganizationName != "" {
		// Generate slug from organization name
		slug := generateSlug(*requestData.OrganizationName)

		// Check if organization slug already exists
		existing, err := h.organizationRepo.GetBySlug(c.Request.Context(), slug)
		if err == nil && existing != nil {
			// If organization exists, just add user as member
			organization = existing
		} else {
			// Create new organization
			organization = &entities.Organization{
				Name:   *requestData.OrganizationName,
				Slug:   slug,
				Email:  requestData.Email,
				Type:   entities.OrganizationTypeVendor,
				Status: entities.OrganizationStatusActive,
			}

			if err := h.organizationRepo.Create(c.Request.Context(), organization); err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create organization", "reason": err.Error()})
				return
			}
		}

		// Add user to organization
		userOrg := &entities.UsersOrganization{
			UserID:         user.ID,
			OrganizationID: organization.ID,
			JoinedAt:       time.Now(),
		}

		if err := h.usersOrgRepo.Create(c.Request.Context(), userOrg); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to add user to organization", "reason": err.Error()})
			return
		}

		// Grant organization admin permissions
		orgScope := "org:" + organization.ID
		err = h.authService.GrantPermission(c.Request.Context(), user.ID, orgScope, "*", []string{"*"}, user.ID)
		if err != nil {
			// Log error but don't fail registration
		}
	}

	// Remove sensitive information
	user.Password = ""

	response := gin.H{
		"user":    user,
		"message": "user created successfully",
	}

	if organization != nil {
		response["organization"] = organization
	}

	c.JSON(http.StatusCreated, response)
}

// Logout invalidates the current session
func (h *AuthHandler) Logout(c *gin.Context) {
	sessionID := c.GetString("session_id")
	if sessionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "no active session"})
		return
	}

	// Delete session
	if err := h.sessionRepo.Delete(c.Request.Context(), parseUUID(sessionID)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to logout", "reason": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "logged out successfully"})
}

// RefreshToken generates a new JWT token using existing session
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	userID := c.GetString("user_id")
	sessionID := c.GetString("session_id")

	if userID == "" || sessionID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid session"})
		return
	}

	// Verify session exists and is valid
	session, err := h.sessionRepo.GetByID(c.Request.Context(), parseUUID(sessionID))
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "session not found"})
		return
	}

	if session.ExpiresAt.Before(time.Now()) {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "session expired"})
		return
	}

	// Extend session
	session.ExpiresAt = time.Now().Add(24 * time.Hour)
	if err := h.sessionRepo.Update(c.Request.Context(), session); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to extend session"})
		return
	}

	// Generate new JWT token
	token, err := h.authService.GenerateJWT(userID, sessionID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to generate token", "reason": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"token":      token,
		"expires_at": session.ExpiresAt,
	})
}

// generateSlug creates a URL-friendly slug from organization name
func generateSlug(name string) string {
	// Simple slug generation - convert to lowercase and replace spaces with hyphens
	slug := ""
	for _, r := range name {
		if r >= 'A' && r <= 'Z' {
			slug += string(r + 32) // Convert to lowercase
		} else if r >= 'a' && r <= 'z' || r >= '0' && r <= '9' {
			slug += string(r)
		} else if r == ' ' || r == '_' {
			slug += "-"
		}
	}
	return slug
}
