package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"

	// Import docs for swagger
	_ "github.com/gokeys/gokeys/internal/adapters/http/docs"
)

// MachineHandler xử lý các HTTP requests liên quan đến machine
// Machine trong keygen-api đại diện cho một máy tính/thiết bị chạy license
// Bao gồm fingerprinting, heartbeat monitoring, và component tracking
type MachineHandler struct {
	machineRepo repositories.MachineRepository // Repository để thao tác với machine trong database
	licenseRepo repositories.LicenseRepository // Repository để validate license tồn tại
	policyRepo  repositories.PolicyRepository  // Repository để lấy policy rules
	userRepo    repositories.UserRepository    // Repository để validate user ownership
}

// NewMachineHandler tạo một machine handler mới
// Cần tất cả repositories để validate relationships và business rules
func NewMachineHandler(
	machineRepo repositories.MachineRepository,
	licenseRepo repositories.LicenseRepository,
	policyRepo repositories.PolicyRepository,
	userRepo repositories.UserRepository,
) *MachineHandler {
	return &MachineHandler{
		machineRepo: machineRepo,
		licenseRepo: licenseRepo,
		policyRepo:  policyRepo,
		userRepo:    userRepo,
	}
}

// ListMachines liệt kê tất cả machines với khả năng filter đa dạng
// Trong keygen-api, machines được scope theo organization và có thể filter theo nhiều tiêu chí
// ListMachines lists machines in an organization
// @Summary List organization machines
// @Description Get paginated list of machines in an organization with filtering options
// @Tags Machines
// @Accept json
// @Produce json
// @Param organization_id path string true "Organization ID" format(uuid)
// @Param fingerprint query string false "Filter by machine fingerprint"
// @Param ip query string false "Filter by IP address"
// @Param status query string false "Filter by machine status" Enums(active,inactive,suspended)
// @Param page query int false "Page number" default(1)
// @Param page_size query int false "Page size" default(20)
// @Success 200 {object} docs.PaginatedMachineResponse "List of machines with pagination"
// @Failure 400 {object} docs.ErrorResponse "Invalid request parameters"
// @Failure 401 {object} docs.ErrorResponse "Authentication required"
// @Failure 403 {object} docs.ErrorResponse "Access denied"
// @Failure 404 {object} docs.ErrorResponse "Organization not found"
// @Failure 500 {object} docs.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /organizations/{organization_id}/machines [get]
func (h *MachineHandler) ListMachines(c *gin.Context) {
	// Lấy organization_id từ URL path parameter
	// Tất cả machines phải thuộc về organization này
	organizationID := c.Param("organization_id")

	// === PAGINATION PARAMETERS ===
	// Parse các tham số phân trang từ query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))           // Trang hiện tại, mặc định = 1
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20")) // Số items per page, mặc định = 20

	// === FILTER CONSTRUCTION ===
	// Xây dựng filter cho database query với Ruby has_scope equivalent
	filter := repositories.DefaultListFilter()
	filter.Page = page
	filter.PageSize = pageSize
	filter.Filters["organization_id = ?"] = organizationID // Base organization scoping

	// === MACHINE-SPECIFIC FILTERS ===
	// Các filter dựa trên thuộc tính của machine (tương tự Ruby has_scope)

	// Fingerprint filter - tìm machine theo fingerprint cụ thể
	if fingerprint := c.Query("fingerprint"); fingerprint != "" {
		filter.Filters["fingerprint = ?"] = fingerprint
	}

	// IP filter - tìm machine theo địa chỉ IP
	if ip := c.Query("ip"); ip != "" {
		filter.Filters["ip = ?"] = ip
	}

	// Hostname filter - tìm machine theo hostname
	if hostname := c.Query("hostname"); hostname != "" {
		filter.Filters["hostname = ?"] = hostname
	}

	// Status filter - lọc machines theo trạng thái (active/inactive)
	if status := c.Query("status"); status != "" {
		filter.Filters["status = ?"] = status
	}

	// === RELATIONSHIP FILTERS ===
	// Các filter dựa trên relationships với entities khác

	// Product filter - lọc machines theo product (thông qua policy)
	if productID := c.Query("product"); productID != "" {
		filter.Filters["product_id = ?"] = productID
	}

	// Policy filter - lọc machines theo policy cụ thể
	if policyID := c.Query("policy"); policyID != "" {
		filter.Filters["policy_id = ?"] = policyID
	}

	// License filter - lọc machines theo license cụ thể
	if licenseID := c.Query("license"); licenseID != "" {
		filter.Filters["license_id = ?"] = licenseID
	}

	// License key filter - tìm machines theo license key (Ruby: for_key scope)
	if licenseKey := c.Query("key"); licenseKey != "" {
		// Cần join với licenses table để access key field
		filter.Filters["licenses.key = ?"] = licenseKey
	}

	// Owner filter - lọc machines theo user sở hữu
	if ownerID := c.Query("owner"); ownerID != "" {
		filter.Filters["owner_id = ?"] = ownerID
	}

	// User filter - alias cho owner filter (Ruby: for_user scope maps to owner_id)
	if userID := c.Query("user"); userID != "" {
		filter.Filters["owner_id = ?"] = userID
	}

	// === DATABASE QUERY ===
	// Thực hiện query database để lấy danh sách machines với tất cả filters
	machines, total, err := h.machineRepo.List(c.Request.Context(), filter)
	if err != nil {
		// Trả về lỗi 500 nếu có vấn đề với database
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get machines", "reason": err.Error()})
		return
	}

	// === PAGINATION CALCULATION ===
	// Tính toán metadata cho pagination
	totalPages := (int(total) + pageSize - 1) / pageSize // ceil(total / pageSize)

	// === SUCCESS RESPONSE ===
	// Trả về danh sách machines với pagination metadata
	c.JSON(http.StatusOK, gin.H{
		"machines": machines, // Danh sách machines với tất cả thông tin
		"pagination": gin.H{
			"page":        page,       // Trang hiện tại
			"page_size":   pageSize,   // Số items per page
			"total":       total,      // Tổng số machines
			"total_pages": totalPages, // Tổng số trang
		},
	})
}

// GetMachine lấy thông tin chi tiết của một machine cụ thể theo ID
// Endpoint: GET /organizations/{org_id}/machines/{machine_id}
// Trả về đầy đủ thông tin machine bao gồm relationships và metadata
func (h *MachineHandler) GetMachine(c *gin.Context) {
	// Lấy machine_id từ URL path parameter
	machineID := c.Param("machine_id")

	// === MACHINE ID VALIDATION ===
	// Parse machine_id thành UUID
	// Trong keygen-api, tất cả IDs đều là UUID format
	machineUUID, err := uuid.Parse(machineID)
	if err != nil {
		// Trả về lỗi 400 nếu machine_id không phải UUID hợp lệ
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid machine ID"})
		return
	}

	// === MACHINE RETRIEVAL ===
	// Tìm machine trong database theo UUID
	machine, err := h.machineRepo.GetByID(c.Request.Context(), machineUUID)
	if err != nil {
		// Trả về lỗi 404 nếu không tìm thấy machine
		// Có thể do machine không tồn tại hoặc không thuộc về organization này
		c.JSON(http.StatusNotFound, gin.H{"error": "machine not found"})
		return
	}

	// === SUCCESS RESPONSE ===
	// Trả về machine với tất cả thông tin chi tiết
	// Bao gồm components, heartbeat status, relationships, và metadata
	c.JSON(http.StatusOK, gin.H{"machine": machine})
}

// CreateMachine creates a new machine following keygen-api patterns
// @Summary Create machine
// @Description Create a new machine for license activation and tracking (admin access required)
// @Tags Machines
// @Accept json
// @Produce json
// @Param organization_id path string true "Organization ID" format(uuid)
// @Param request body docs.CreateMachineRequest true "Machine creation details"
// @Success 201 {object} docs.MachineResponse "Machine created successfully"
// @Failure 400 {object} docs.ErrorResponse "Invalid request format or validation errors"
// @Failure 401 {object} docs.ErrorResponse "Authentication required"
// @Failure 403 {object} docs.ErrorResponse "Admin access required"
// @Failure 404 {object} docs.ErrorResponse "Organization or license not found"
// @Failure 409 {object} docs.ErrorResponse "Machine fingerprint already exists"
// @Failure 500 {object} docs.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /organizations/{organization_id}/machines [post]
func (h *MachineHandler) CreateMachine(c *gin.Context) {
	organizationID := c.Param("organization_id")

	var requestData struct {
		// Core attributes mapping from Ruby typed_params
		LicenseID   string            `json:"license_id" binding:"required"`
		Fingerprint string            `json:"fingerprint" binding:"required"`
		Name        *string           `json:"name"`
		IP          *string           `json:"ip"`
		Hostname    *string           `json:"hostname"`
		Platform    *string           `json:"platform"`
		Cores       *int              `json:"cores"`
		OwnerID     *string           `json:"owner_id"`
		Metadata    entities.Metadata `json:"metadata"`

		// Machine components (Ruby: relationships.components)
		Components []struct {
			Fingerprint string            `json:"fingerprint" binding:"required"`
			Name        string            `json:"name" binding:"required"`
			Metadata    entities.Metadata `json:"metadata"`
		} `json:"components"`
	}

	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request", "reason": err.Error()})
		return
	}

	// Validate license exists and belongs to organization
	licenseUUID, err := uuid.Parse(requestData.LicenseID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid license ID"})
		return
	}

	license, err := h.licenseRepo.GetByID(c.Request.Context(), licenseUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "license not found"})
		return
	}

	if license.OrganizationID != organizationID {
		c.JSON(http.StatusBadRequest, gin.H{"error": "license does not belong to this organization"})
		return
	}

	// Validate owner if specified
	if requestData.OwnerID != nil {
		ownerUUID, err := uuid.Parse(*requestData.OwnerID)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid owner ID"})
			return
		}

		_, err = h.userRepo.GetByID(c.Request.Context(), ownerUUID)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "owner not found"})
			return
		}
	}

	// === MACHINE ENTITY CREATION ===
	// Tạo machine entity với tất cả thông tin cần thiết
	now := time.Now()
	machine := &entities.Machine{
		LicenseID:   requestData.LicenseID,
		PolicyID:    license.PolicyID,
		Fingerprint: requestData.Fingerprint,
		Name:        requestData.Name,
		IP:          requestData.IP,
		Hostname:    requestData.Hostname,
		Platform:    requestData.Platform,
		Cores:       1,                            // Default to 1 core
		Status:      entities.MachineStatusActive, // Machine active khi tạo
		ActivatedAt: &now,
		Metadata:    requestData.Metadata,
		License:     *license, // Set license relation để có thể access policy
	}

	// Set optional fields
	if requestData.Cores != nil {
		machine.Cores = *requestData.Cores
	}
	if requestData.OwnerID != nil {
		machine.OwnerID = requestData.OwnerID
	}

	// === COMPONENTS PROCESSING ===
	// Xử lý machine components nếu được cung cấp
	if len(requestData.Components) > 0 {
		components := make(entities.MachineComponents)
		for i, comp := range requestData.Components {
			// Sử dụng name làm key, fingerprint làm value
			components[comp.Name] = comp.Fingerprint

			// Nếu là component đầu tiên và chưa có fingerprint, dùng làm machine fingerprint
			if i == 0 && machine.Fingerprint == "" {
				machine.Fingerprint = comp.Fingerprint
			}
		}
		machine.UpdateComponents(components)
	}

	// === BUSINESS LOGIC VALIDATION ===
	// Validate machine theo business rules
	if validationErrors := machine.ValidateMachine(); len(validationErrors) > 0 {
		c.JSON(http.StatusUnprocessableEntity, gin.H{
			"error":  "validation failed",
			"errors": validationErrors,
		})
		return
	}

	// === DATABASE SAVE ===
	// Lưu machine vào database
	if err := h.machineRepo.Create(c.Request.Context(), machine); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create machine", "reason": err.Error()})
		return
	}

	// === POST-CREATION TASKS ===
	// TODO: Handle activation token increment (Ruby: current_token.increment :activations)
	// TODO: Broadcast machine.created event (Ruby: BroadcastEventService.call)

	c.JSON(http.StatusCreated, gin.H{"machine": machine})
}

// UpdateMachine updates an existing machine following keygen-api patterns
func (h *MachineHandler) UpdateMachine(c *gin.Context) {
	machineID := c.Param("machine_id")

	machineUUID, err := uuid.Parse(machineID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid machine ID"})
		return
	}

	machine, err := h.machineRepo.GetByID(c.Request.Context(), machineUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "machine not found"})
		return
	}

	var requestData struct {
		// Update fields mapping from Ruby typed_params for update action
		Name     *string            `json:"name"`
		IP       *string            `json:"ip"`
		Hostname *string            `json:"hostname"`
		Platform *string            `json:"platform"`
		Cores    *int               `json:"cores"`
		Metadata *entities.Metadata `json:"metadata"`
	}

	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request", "reason": err.Error()})
		return
	}

	// Update fields if provided (Ruby: machine.update(machine_params))
	if requestData.Name != nil {
		machine.Name = requestData.Name
	}
	if requestData.IP != nil {
		machine.IP = requestData.IP
	}
	if requestData.Hostname != nil {
		machine.Hostname = requestData.Hostname
	}
	if requestData.Platform != nil {
		machine.Platform = requestData.Platform
	}
	if requestData.Cores != nil {
		machine.Cores = *requestData.Cores
	}
	if requestData.Metadata != nil {
		machine.Metadata = *requestData.Metadata
	}

	if err := h.machineRepo.Update(c.Request.Context(), machine); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to update machine", "reason": err.Error()})
		return
	}

	// TODO: Broadcast machine.updated event (Ruby: BroadcastEventService.call)

	c.JSON(http.StatusOK, gin.H{"machine": machine})
}

// DeleteMachine deletes a machine
func (h *MachineHandler) DeleteMachine(c *gin.Context) {
	machineID := c.Param("machine_id")

	machineUUID, err := uuid.Parse(machineID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid machine ID"})
		return
	}

	_, err = h.machineRepo.GetByID(c.Request.Context(), machineUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "machine not found"})
		return
	}

	// TODO: Handle activation token decrement (Ruby: current_token.decrement :activations)
	// TODO: Broadcast machine.deleted event (Ruby: BroadcastEventService.call)

	if err := h.machineRepo.Delete(c.Request.Context(), machineUUID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to delete machine", "reason": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "machine deleted successfully"})
}

// MachineHeartbeat updates machine heartbeat (Ruby: machines/actions/heartbeats_controller.rb)
// @Summary Machine heartbeat
// @Description Update machine heartbeat to indicate it's still active (for license monitoring)
// @Tags Machines
// @Accept json
// @Produce json
// @Param organization_id path string true "Organization ID" format(uuid)
// @Param machine_id path string true "Machine ID" format(uuid)
// @Param request body docs.MachineHeartbeatRequest true "Heartbeat details"
// @Success 200 {object} docs.MachineResponse "Heartbeat updated successfully"
// @Failure 400 {object} docs.ErrorResponse "Invalid machine ID format"
// @Failure 401 {object} docs.ErrorResponse "Authentication required"
// @Failure 403 {object} docs.ErrorResponse "Access denied or machine suspended"
// @Failure 404 {object} docs.ErrorResponse "Machine not found"
// @Failure 500 {object} docs.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /organizations/{organization_id}/machines/{machine_id}/actions/heartbeat [post]
func (h *MachineHandler) MachineHeartbeat(c *gin.Context) {
	machineID := c.Param("machine_id")

	machineUUID, err := uuid.Parse(machineID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid machine ID"})
		return
	}

	// === MACHINE RETRIEVAL ===
	// Lấy machine từ database với policy relation để có thể check business rules
	machine, err := h.machineRepo.GetByID(c.Request.Context(), machineUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "machine not found"})
		return
	}

	// === HEARTBEAT BUSINESS LOGIC ===
	// Check xem machine có require heartbeat không
	if !machine.RequiresHeartbeat() {
		c.JSON(http.StatusUnprocessableEntity, gin.H{
			"error": "machine does not require heartbeat",
		})
		return
	}

	// Update heartbeat timestamp sử dụng business logic
	machine.UpdateHeartbeat()

	// === DATABASE UPDATE ===
	// Lưu heartbeat timestamp vào database
	if err := h.machineRepo.Update(c.Request.Context(), machine); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to update heartbeat", "reason": err.Error()})
		return
	}

	// === EVENT BROADCASTING ===
	// TODO: Broadcast machine.heartbeat event (Ruby: BroadcastEventService.call)

	// === SUCCESS RESPONSE ===
	// Trả về thông tin heartbeat với status
	c.JSON(http.StatusOK, gin.H{
		"message":          "heartbeat updated successfully",
		"machine":          machine,
		"heartbeat_at":     machine.LastHeartbeatAt,
		"next_heartbeat":   machine.NextHeartbeatAt,
		"heartbeat_status": machine.GetHeartbeatStatus(),
	})
}

// ActivateMachine kích hoạt machine (tương tự Ruby machine.activate!)
func (h *MachineHandler) ActivateMachine(c *gin.Context) {
	machineID := c.Param("machine_id")

	// === MACHINE ID VALIDATION ===
	machineUUID, err := uuid.Parse(machineID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid machine ID"})
		return
	}

	// === MACHINE RETRIEVAL ===
	machine, err := h.machineRepo.GetByID(c.Request.Context(), machineUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "machine not found"})
		return
	}

	// === ACTIVATION LOGIC ===
	// Check xem machine đã active chưa
	if machine.IsActive() {
		c.JSON(http.StatusUnprocessableEntity, gin.H{
			"error": "machine is already active",
		})
		return
	}

	// Activate machine sử dụng business logic
	machine.Activate()

	// === DATABASE UPDATE ===
	if err := h.machineRepo.Update(c.Request.Context(), machine); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to activate machine", "reason": err.Error()})
		return
	}

	// === SUCCESS RESPONSE ===
	c.JSON(http.StatusOK, gin.H{
		"message": "machine activated successfully",
		"machine": machine,
	})
}

// DeactivateMachine vô hiệu hóa machine (tương tự Ruby machine.deactivate!)
func (h *MachineHandler) DeactivateMachine(c *gin.Context) {
	machineID := c.Param("machine_id")

	// === MACHINE ID VALIDATION ===
	machineUUID, err := uuid.Parse(machineID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid machine ID"})
		return
	}

	// === MACHINE RETRIEVAL ===
	machine, err := h.machineRepo.GetByID(c.Request.Context(), machineUUID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "machine not found"})
		return
	}

	// === DEACTIVATION LOGIC ===
	// Check xem machine có active không
	if machine.IsInactive() {
		c.JSON(http.StatusUnprocessableEntity, gin.H{
			"error": "machine is already inactive",
		})
		return
	}

	// Deactivate machine sử dụng business logic
	machine.Deactivate()

	// === DATABASE UPDATE ===
	if err := h.machineRepo.Update(c.Request.Context(), machine); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to deactivate machine", "reason": err.Error()})
		return
	}

	// === SUCCESS RESPONSE ===
	c.JSON(http.StatusOK, gin.H{
		"message": "machine deactivated successfully",
		"machine": machine,
	})
}

// GetMachineProcesses gets all processes for a machine
func (h *MachineHandler) GetMachineProcesses(c *gin.Context) {
	machineID := c.Param("machine_id")

	_, err := uuid.Parse(machineID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid machine ID"})
		return
	}

	// TODO: Implement when MachineProcess repository is available
	// processes, err := h.machineProcessRepo.GetByMachineID(c.Request.Context(), machineID)

	c.JSON(http.StatusOK, gin.H{
		"machine_id": machineID,
		"processes":  []interface{}{}, // Placeholder
	})
}

// GetMachineComponents gets all components for a machine
func (h *MachineHandler) GetMachineComponents(c *gin.Context) {
	machineID := c.Param("machine_id")

	_, err := uuid.Parse(machineID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid machine ID"})
		return
	}

	// TODO: Implement when MachineComponent repository is available
	// components, err := h.machineComponentRepo.GetByMachineID(c.Request.Context(), machineID)

	c.JSON(http.StatusOK, gin.H{
		"machine_id": machineID,
		"components": []interface{}{}, // Placeholder
	})
}
