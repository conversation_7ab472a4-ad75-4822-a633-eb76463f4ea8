package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services/auth"

	// Import docs for swagger
	_ "github.com/gokeys/gokeys/internal/adapters/http/docs"
)

// UserHandler handles user-related HTTP requests
type UserHandler struct {
	userRepo         repositories.UserRepository
	usersOrgRepo     repositories.UsersOrganizationRepository
	permissionRepo   repositories.PermissionRepository
	organizationRepo repositories.OrganizationRepository
	authService      *auth.AuthService
}

// NewUserHandler creates a new user handler
func NewUserHandler(
	userRepo repositories.UserRepository,
	usersOrgRepo repositories.UsersOrganizationRepository,
	permissionRepo repositories.PermissionRepository,
	organizationRepo repositories.OrganizationRepository,
	authService *auth.AuthService,
) *UserHandler {
	return &UserHandler{
		userRepo:         userRepo,
		usersOrgRepo:     usersOrgRepo,
		permissionRepo:   permissionRepo,
		organizationRepo: organizationRepo,
		authService:      authService,
	}
}

// GetCurrentUser gets the current user's information
// @Summary Get current user profile
// @Description Get the authenticated user's profile information
// @Tags User Profile
// @Accept json
// @Produce json
// @Success 200 {object} docs.UserResponse "User profile information"
// @Failure 401 {object} docs.ErrorResponse "Authentication required"
// @Failure 404 {object} docs.ErrorResponse "User not found"
// @Failure 500 {object} docs.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /user/profile [get]
func (h *UserHandler) GetCurrentUser(c *gin.Context) {
	userID := c.GetString("user_id")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized", "reason": "no user context"})
		return
	}

	user, err := h.userRepo.GetByID(c.Request.Context(), parseUUID(userID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "user not found", "reason": err.Error()})
		return
	}

	// Remove sensitive information
	user.Password = ""
	user.TOTPSecret = nil
	user.PasswordResetToken = nil

	c.JSON(http.StatusOK, gin.H{"user": user})
}

// UpdateCurrentUser updates the current user's information
// @Summary Update current user profile
// @Description Update the authenticated user's profile information
// @Tags User Profile
// @Accept json
// @Produce json
// @Param request body docs.UpdateUserRequest true "User profile update details"
// @Success 200 {object} docs.UserResponse "Updated user profile"
// @Failure 400 {object} docs.ErrorResponse "Invalid request format or validation errors"
// @Failure 401 {object} docs.ErrorResponse "Authentication required"
// @Failure 404 {object} docs.ErrorResponse "User not found"
// @Failure 409 {object} docs.ErrorResponse "Email already exists"
// @Failure 500 {object} docs.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /user/profile [put]
func (h *UserHandler) UpdateCurrentUser(c *gin.Context) {
	userID := c.GetString("user_id")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized", "reason": "no user context"})
		return
	}

	var updateData struct {
		FirstName *string `json:"first_name"`
		LastName  *string `json:"last_name"`
		Email     *string `json:"email"`
	}

	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request", "reason": err.Error()})
		return
	}

	user, err := h.userRepo.GetByID(c.Request.Context(), parseUUID(userID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "user not found", "reason": err.Error()})
		return
	}

	// Update fields if provided
	if updateData.FirstName != nil {
		user.FirstName = updateData.FirstName
	}
	if updateData.LastName != nil {
		user.LastName = updateData.LastName
	}
	if updateData.Email != nil {
		user.Email = *updateData.Email
	}

	if err := h.userRepo.Update(c.Request.Context(), user); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to update user", "reason": err.Error()})
		return
	}

	// Remove sensitive information
	user.Password = ""
	user.TOTPSecret = nil
	user.PasswordResetToken = nil

	c.JSON(http.StatusOK, gin.H{"user": user})
}

// GetUserOrganizations gets the current user's organizations
func (h *UserHandler) GetUserOrganizations(c *gin.Context) {
	userID := c.GetString("user_id")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized", "reason": "no user context"})
		return
	}

	userOrgs, err := h.authService.GetUserOrganizations(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get user organizations", "reason": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"organizations": userOrgs})
}

// GetUserPermissions gets the current user's permissions
func (h *UserHandler) GetUserPermissions(c *gin.Context) {
	userID := c.GetString("user_id")
	if userID == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized", "reason": "no user context"})
		return
	}

	permissions, err := h.permissionRepo.GetUserPermissions(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get user permissions", "reason": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"permissions": permissions})
}

// ListOrganizationUsers lists users in an organization
// @Summary List organization users
// @Description Get paginated list of users in an organization (admin access required)
// @Tags Organization Users
// @Accept json
// @Produce json
// @Param organization_id path string true "Organization ID" format(uuid)
// @Param page query int false "Page number" default(1)
// @Param page_size query int false "Page size" default(20)
// @Param search query string false "Search users by name or email"
// @Param role query string false "Filter by user role" Enums(admin,member,viewer)
// @Success 200 {object} docs.PaginatedUserResponse "List of organization users with pagination"
// @Failure 400 {object} docs.ErrorResponse "Invalid request parameters"
// @Failure 401 {object} docs.ErrorResponse "Authentication required"
// @Failure 403 {object} docs.ErrorResponse "Admin access required"
// @Failure 404 {object} docs.ErrorResponse "Organization not found"
// @Failure 500 {object} docs.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /organizations/{organization_id}/users [get]
func (h *UserHandler) ListOrganizationUsers(c *gin.Context) {
	organizationID := c.Param("organization_id")
	if organizationID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "organization_id required"})
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	filter := repositories.DefaultListFilter()
	filter.Page = page
	filter.PageSize = pageSize

	if page > 0 {
		filter.Page = page
	}
	if pageSize > 0 && pageSize <= 100 {
		filter.PageSize = pageSize
	}

	// Get organization users through membership
	userOrgs, err := h.usersOrgRepo.GetOrganizationUsers(c.Request.Context(), organizationID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get organization users", "reason": err.Error()})
		return
	}

	// Get user details
	var users []interface{}
	for _, userOrg := range userOrgs {
		user, err := h.userRepo.GetByID(c.Request.Context(), parseUUID(userOrg.UserID))
		if err != nil {
			continue // Skip users that can't be loaded
		}

		// Remove sensitive information
		user.Password = ""
		user.TOTPSecret = nil
		user.PasswordResetToken = nil

		users = append(users, gin.H{
			"user":       user,
			"joined_at":  userOrg.JoinedAt,
			"invited_by": userOrg.InvitedBy,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"users": users,
		"pagination": gin.H{
			"page":      page,
			"page_size": pageSize,
			"total":     len(users),
		},
	})
}

// GetOrganizationUser gets a specific user in an organization
func (h *UserHandler) GetOrganizationUser(c *gin.Context) {
	organizationID := c.Param("organization_id")
	userID := c.Param("user_id")

	if organizationID == "" || userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "organization_id and user_id required"})
		return
	}

	// Check if user is in organization
	userOrg, err := h.usersOrgRepo.GetByUserAndOrganization(c.Request.Context(), userID, organizationID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "user not found in organization", "reason": err.Error()})
		return
	}

	// Get user details
	user, err := h.userRepo.GetByID(c.Request.Context(), parseUUID(userID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "user not found", "reason": err.Error()})
		return
	}

	// Remove sensitive information
	user.Password = ""
	user.TOTPSecret = nil
	user.PasswordResetToken = nil

	c.JSON(http.StatusOK, gin.H{
		"user":       user,
		"membership": userOrg,
	})
}

// AddUserToOrganization adds a user to an organization
// @Summary Add user to organization
// @Description Add a user to an organization with specified role (admin access required)
// @Tags Organization Users
// @Accept json
// @Produce json
// @Param organization_id path string true "Organization ID" format(uuid)
// @Param request body docs.AddUserToOrganizationRequest true "User addition details"
// @Success 201 {object} docs.UserOrganizationResponse "User added to organization successfully"
// @Failure 400 {object} docs.ErrorResponse "Invalid request format or validation errors"
// @Failure 401 {object} docs.ErrorResponse "Authentication required"
// @Failure 403 {object} docs.ErrorResponse "Admin access required"
// @Failure 404 {object} docs.ErrorResponse "Organization or user not found"
// @Failure 409 {object} docs.ErrorResponse "User already in organization"
// @Failure 500 {object} docs.ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /organizations/{organization_id}/users [post]
func (h *UserHandler) AddUserToOrganization(c *gin.Context) {
	organizationID := c.Param("organization_id")
	currentUserID := c.GetString("user_id")

	if organizationID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "organization_id required"})
		return
	}

	var requestData struct {
		UserID string `json:"user_id" binding:"required"`
		Email  string `json:"email"`
	}

	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request", "reason": err.Error()})
		return
	}

	targetUserID := requestData.UserID

	// If email is provided instead of user_id, find user by email
	if requestData.Email != "" && targetUserID == "" {
		user, err := h.userRepo.GetByEmail(c.Request.Context(), requestData.Email)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "user not found", "reason": err.Error()})
			return
		}
		targetUserID = user.ID
	}

	// Check if user is already in organization
	exists, err := h.authService.IsUserInOrganization(c.Request.Context(), targetUserID, organizationID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to check user membership", "reason": err.Error()})
		return
	}
	if exists {
		c.JSON(http.StatusConflict, gin.H{"error": "user already in organization"})
		return
	}

	// Add user to organization
	err = h.authService.AddUserToOrganization(c.Request.Context(), targetUserID, organizationID, currentUserID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to add user to organization", "reason": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"message": "user added to organization successfully"})
}

// RemoveUserFromOrganization removes a user from an organization
func (h *UserHandler) RemoveUserFromOrganization(c *gin.Context) {
	organizationID := c.Param("organization_id")
	userID := c.Param("user_id")

	if organizationID == "" || userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "organization_id and user_id required"})
		return
	}

	// Remove user from organization
	err := h.authService.RemoveUserFromOrganization(c.Request.Context(), userID, organizationID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to remove user from organization", "reason": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "user removed from organization successfully"})
}

// GetUserPermissions gets permissions for a user in an organization
func (h *UserHandler) GetUserPermissionsInOrganization(c *gin.Context) {
	organizationID := c.Param("organization_id")
	userID := c.Param("user_id")

	if organizationID == "" || userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "organization_id and user_id required"})
		return
	}

	permissions, err := h.permissionRepo.GetUserPermissions(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get user permissions", "reason": err.Error()})
		return
	}

	// Filter permissions related to the organization
	var orgPermissions []interface{}
	orgScope := "org:" + organizationID

	for _, permission := range permissions {
		if permission.Scope == "system" || permission.Scope == orgScope {
			orgPermissions = append(orgPermissions, permission)
		}
	}

	c.JSON(http.StatusOK, gin.H{"permissions": orgPermissions})
}

// GrantUserPermission grants a permission to a user
func (h *UserHandler) GrantUserPermission(c *gin.Context) {
	organizationID := c.Param("organization_id")
	userID := c.Param("user_id")
	currentUserID := c.GetString("user_id")

	if organizationID == "" || userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "organization_id and user_id required"})
		return
	}

	var requestData struct {
		Scope        string   `json:"scope" binding:"required"`
		ResourceType string   `json:"resource_type" binding:"required"`
		Actions      []string `json:"actions" binding:"required"`
	}

	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request", "reason": err.Error()})
		return
	}

	// Grant permission
	err := h.authService.GrantPermission(c.Request.Context(), userID, requestData.Scope, requestData.ResourceType, requestData.Actions, currentUserID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to grant permission", "reason": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"message": "permission granted successfully"})
}

// RevokeUserPermission revokes a permission from a user
func (h *UserHandler) RevokeUserPermission(c *gin.Context) {
	organizationID := c.Param("organization_id")
	userID := c.Param("user_id")

	if organizationID == "" || userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "organization_id and user_id required"})
		return
	}

	var requestData struct {
		Scope        string `json:"scope" binding:"required"`
		ResourceType string `json:"resource_type" binding:"required"`
	}

	if err := c.ShouldBindJSON(&requestData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request", "reason": err.Error()})
		return
	}

	// Revoke permission
	err := h.authService.RevokePermission(c.Request.Context(), userID, requestData.Scope, requestData.ResourceType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to revoke permission", "reason": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "permission revoked successfully"})
}
