package entities

import (
	"fmt"
)

// ===== POLICY STRATEGY TYPES - GOLANG STYLE =====
// Sử dụng custom types để type safety và validation mạnh mẽ

// CryptoScheme - Scheme mã hóa/ký số cho license keys
//
// Quyết định cách thức bảo mật và xác thực license keys:
// - Không có scheme: License key được lưu trữ dạng plain text (không bảo mật)
// - Có scheme: License key được mã hóa/ký số để chống giả mạo và offline validation
//
// Use cases:
// - Offline licensing: Phần mềm có thể validate license mà không cần internet
// - Anti-tampering: Ngăn chặn user chỉnh sửa license key
// - Cryptographic proof: Chứng minh license key được phát hành bởi server chính thức
//
// Go implementation chỉ hỗ trợ modern schemes (không có legacy compatibility):
// - ED25519: Elliptic curve, nhanh nhất, bảo mật cao nhất (recommended)
// - RSA 2048: Tư<PERSON>ng thích rộng rãi, chậm hơn ED25519
// - JWT RS256: Chuẩn JWT với RSA signature, dễ integrate với existing systems
type CryptoScheme string

const (
	// Modern schemes only - Go implementation uses latest techniques
	CryptoSchemeRSA2048JWT  CryptoScheme = "RSA_2048_JWT_RS256"      // JWT với RSA-256 signature
	CryptoSchemeRSA2048Sign CryptoScheme = "RSA_2048_PKCS1_SIGN"     // RSA PKCS1 v1.5 signature (latest format)
	CryptoSchemeRSA2048PSS  CryptoScheme = "RSA_2048_PKCS1_PSS_SIGN" // RSA PSS signature (latest format)

	// ED25519 (recommended default - fastest and most secure)
	CryptoSchemeED25519 CryptoScheme = "ED25519_SIGN" // Elliptic curve signature (recommended)
)

// IsValid kiểm tra CryptoScheme có hợp lệ không (modern schemes only)
func (cs CryptoScheme) IsValid() bool {
	switch cs {
	case CryptoSchemeRSA2048JWT, CryptoSchemeRSA2048Sign, CryptoSchemeRSA2048PSS, CryptoSchemeED25519:
		return true
	}
	return false
}

// String trả về string representation
func (cs CryptoScheme) String() string {
	return string(cs)
}

// MachineUniquenessStrategy - Chiến lược kiểm soát tính duy nhất của machine fingerprint
//
// Machine fingerprint là "dấu vân tay" định danh duy nhất của một máy tính (CPU ID, MAC address, etc.)
// Strategy này quyết định phạm vi kiểm tra trùng lặp fingerprint để ngăn chặn:
// - Chia sẻ license trái phép giữa nhiều máy
// - Lạm dụng trial license (tạo nhiều trial cho cùng 1 máy)
// - Fraud detection và license compliance
//
// Ví dụ thực tế:
// - UNIQUE_PER_ACCOUNT: Máy A không thể dùng cho bất kỳ license nào khác trong toàn bộ account
// - UNIQUE_PER_PRODUCT: Máy A không thể dùng cho license khác của cùng product
// - UNIQUE_PER_POLICY: Máy A không thể dùng cho license khác của cùng policy (hữu ích cho trial)
// - UNIQUE_PER_LICENSE: Máy A chỉ không thể duplicate trong cùng license (loose nhất)
type MachineUniquenessStrategy string

const (
	MachineUniquePerAccount MachineUniquenessStrategy = "UNIQUE_PER_ACCOUNT" // Nghiêm ngặt nhất - toàn account
	MachineUniquePerProduct MachineUniquenessStrategy = "UNIQUE_PER_PRODUCT" // Nghiêm ngặt - trong product
	MachineUniquePerPolicy  MachineUniquenessStrategy = "UNIQUE_PER_POLICY"  // Vừa phải - trong policy
	MachineUniquePerLicense MachineUniquenessStrategy = "UNIQUE_PER_LICENSE" // Lỏng nhất - trong license (default)
)

// IsValid kiểm tra strategy có hợp lệ không
func (mus MachineUniquenessStrategy) IsValid() bool {
	switch mus {
	case MachineUniquePerAccount, MachineUniquePerProduct, MachineUniquePerPolicy, MachineUniquePerLicense:
		return true
	}
	return false
}

// Rank trả về mức độ nghiêm ngặt của strategy (số cao hơn = nghiêm ngặt hơn)
//
// Dùng để so sánh và validate compatibility giữa các strategies.
// Ví dụ: Policy A có rank 4, Policy B có rank 2 → Policy A nghiêm ngặt hơn
//
// Ranking logic:
// - UNIQUE_PER_ACCOUNT (4): Nghiêm ngặt nhất - machine unique trong toàn account
// - UNIQUE_PER_PRODUCT (3): Nghiêm ngặt - machine unique trong product
// - UNIQUE_PER_POLICY (2): Vừa phải - machine unique trong policy
// - UNIQUE_PER_LICENSE (1): Lỏng nhất - machine unique trong license
func (mus MachineUniquenessStrategy) Rank() int {
	switch mus {
	case MachineUniquePerAccount:
		return 4 // Nghiêm ngặt nhất
	case MachineUniquePerProduct:
		return 3 // Nghiêm ngặt
	case MachineUniquePerPolicy:
		return 2 // Vừa phải
	case MachineUniquePerLicense:
		return 1 // Lỏng nhất
	}
	return -1 // Invalid strategy
}

// String trả về string representation
func (mus MachineUniquenessStrategy) String() string {
	return string(mus)
}

// ComponentUniquenessStrategy - Chiến lược kiểm soát tính duy nhất của hardware component fingerprint
//
// Component fingerprint là "dấu vân tay" của từng linh kiện phần cứng cụ thể:
// - CPU serial number, motherboard ID, hard drive serial, RAM module ID, etc.
// - Chi tiết hơn machine fingerprint (machine = tổng hợp nhiều components)
// - Khó thay đổi hơn machine fingerprint (phải thay phần cứng thật)
//
// Strategy này quyết định phạm vi kiểm tra trùng lặp component để:
// - Ngăn chặn hardware cloning/spoofing
// - Phát hiện trial abuse ngay cả khi reinstall OS
// - Kiểm soát license binding ở mức hardware thật
// - Anti-piracy mạnh mẽ hơn machine fingerprint
//
// Ví dụ thực tế:
// - User cài trial, xóa rồi cài lại → machine fingerprint thay đổi nhưng CPU ID không đổi
// - UNIQUE_PER_POLICY sẽ phát hiện và block trial thứ 2 trên cùng hardware
// - Ngăn chặn "trial reset" bằng cách format máy
type ComponentUniquenessStrategy string

const (
	ComponentUniquePerAccount ComponentUniquenessStrategy = "UNIQUE_PER_ACCOUNT" // Nghiêm ngặt nhất - toàn account
	ComponentUniquePerProduct ComponentUniquenessStrategy = "UNIQUE_PER_PRODUCT" // Nghiêm ngặt - trong product
	ComponentUniquePerPolicy  ComponentUniquenessStrategy = "UNIQUE_PER_POLICY"  // Vừa phải - trong policy (tốt cho trial)
	ComponentUniquePerLicense ComponentUniquenessStrategy = "UNIQUE_PER_LICENSE" // Lỏng - trong license
	ComponentUniquePerMachine ComponentUniquenessStrategy = "UNIQUE_PER_MACHINE" // Lỏng nhất - trong machine (default)
)

// IsValid kiểm tra strategy có hợp lệ không
func (cus ComponentUniquenessStrategy) IsValid() bool {
	switch cus {
	case ComponentUniquePerAccount, ComponentUniquePerProduct, ComponentUniquePerPolicy,
		ComponentUniquePerLicense, ComponentUniquePerMachine:
		return true
	}
	return false
}

// Rank trả về mức độ nghiêm ngặt
func (cus ComponentUniquenessStrategy) Rank() int {
	switch cus {
	case ComponentUniquePerAccount:
		return 4
	case ComponentUniquePerProduct:
		return 3
	case ComponentUniquePerPolicy:
		return 2
	case ComponentUniquePerLicense:
		return 1
	case ComponentUniquePerMachine:
		return 0
	}
	return -1
}

// String trả về string representation
func (cus ComponentUniquenessStrategy) String() string {
	return string(cus)
}

// MatchingStrategy - Chiến lược matching fingerprint khi validate license
//
// Khi client validate license, nó gửi lên danh sách fingerprints của machine hiện tại.
// Server so sánh với fingerprints đã lưu của license để xác định có phải cùng máy không.
// Strategy này quyết định cần bao nhiêu fingerprints phải match để coi là "cùng máy".
//
// Tại sao cần flexibility:
// - Hardware có thể thay đổi một phần (thêm RAM, thay HDD)
// - OS reinstall có thể làm thay đổi một số fingerprints
// - Virtual machines có fingerprints không ổn định
// - Network adapters có thể bị disable/enable
//
// Ví dụ: Machine có 4 fingerprints [CPU, MAC, HDD, RAM], client gửi [CPU, MAC, SSD, RAM]
// - MATCH_ANY: 1/4 match → PASS (CPU match)
// - MATCH_TWO: 2/4 match → PASS (CPU + RAM match)
// - MATCH_MOST: 3/4 match → FAIL (chỉ có 2/4 match < 50%)
// - MATCH_ALL: 4/4 match → FAIL (HDD != SSD)
type MatchingStrategy string

const (
	MatchAny  MatchingStrategy = "MATCH_ANY"  // Lỏng nhất - chỉ cần 1 fingerprint match (default)
	MatchTwo  MatchingStrategy = "MATCH_TWO"  // Vừa phải - cần ít nhất 2 fingerprints match
	MatchMost MatchingStrategy = "MATCH_MOST" // Nghiêm ngặt - cần >50% fingerprints match
	MatchAll  MatchingStrategy = "MATCH_ALL"  // Nghiêm ngặt nhất - cần tất cả fingerprints match
)

// IsValid kiểm tra strategy có hợp lệ không
func (ms MatchingStrategy) IsValid() bool {
	switch ms {
	case MatchAny, MatchTwo, MatchMost, MatchAll:
		return true
	}
	return false
}

// RequiredMatches tính số fingerprints cần match để validate thành công
//
// Input: totalComponents = số fingerprints mà client gửi lên
// Output: số fingerprints tối thiểu phải match với server records
//
// Logic tính toán:
// - MATCH_ANY: Chỉ cần 1 fingerprint match
// - MATCH_TWO: Cần 2 fingerprints match (hoặc ít hơn nếu total < 2)
// - MATCH_MOST: Cần >50% fingerprints match (công thức: (total / 2) + 1)
// - MATCH_ALL: Cần tất cả fingerprints match
//
// Ví dụ: Client gửi 5 fingerprints
// - MATCH_ANY: cần 1/5 match
// - MATCH_TWO: cần 2/5 match
// - MATCH_MOST: cần 3/5 match (>50%)
// - MATCH_ALL: cần 5/5 match
func (ms MatchingStrategy) RequiredMatches(totalComponents int) int {
	switch ms {
	case MatchAny:
		return 1 // Chỉ cần 1 match
	case MatchTwo:
		return min(2, totalComponents) // Tối đa 2, nhưng không vượt quá total
	case MatchMost:
		return (totalComponents / 2) + 1 // >50% (round up)
	case MatchAll:
		return totalComponents // Tất cả phải match
	}
	return 0 // Invalid strategy
}

// String trả về string representation
func (ms MatchingStrategy) String() string {
	return string(ms)
}

// ExpirationStrategy - Chiến lược xử lý khi license hết hạn (expired)
//
// Khi license đạt đến ngày hết hạn, strategy này quyết định hành vi của hệ thống:
// - Có cho phép tiếp tục sử dụng phần mềm không?
// - Có cho phép download updates/releases mới không?
// - Có cho phép kích hoạt machine mới không?
//
// Use cases thực tế:
// - Software vẫn chạy nhưng không update được (RESTRICT_ACCESS)
// - Software bị khóa hoàn toàn (REVOKE_ACCESS)
// - Grace period cho customer gia hạn (MAINTAIN_ACCESS)
// - Freemium model - vẫn dùng được cơ bản (ALLOW_ACCESS)
//
// Ví dụ: License hết hạn ngày 1/1/2024
// - RESTRICT_ACCESS: Software chạy bình thường, nhưng không download được version mới
// - REVOKE_ACCESS: Software bị khóa hoàn toàn, không thể sử dụng
// - MAINTAIN_ACCESS: Mọi thứ hoạt động bình thường (grace period)
// - ALLOW_ACCESS: Tương tự MAINTAIN_ACCESS nhưng có thể có hạn chế khác
type ExpirationStrategy string

const (
	ExpirationRestrictAccess ExpirationStrategy = "RESTRICT_ACCESS" // Hạn chế truy cập (không download updates) - default
	ExpirationRevokeAccess   ExpirationStrategy = "REVOKE_ACCESS"   // Thu hồi hoàn toàn quyền truy cập
	ExpirationMaintainAccess ExpirationStrategy = "MAINTAIN_ACCESS" // Duy trì quyền truy cập (grace period)
	ExpirationAllowAccess    ExpirationStrategy = "ALLOW_ACCESS"    // Cho phép truy cập (freemium mode)
)

// IsValid kiểm tra strategy có hợp lệ không
func (es ExpirationStrategy) IsValid() bool {
	switch es {
	case ExpirationRestrictAccess, ExpirationRevokeAccess, ExpirationMaintainAccess, ExpirationAllowAccess:
		return true
	}
	return false
}

// AllowsAccess kiểm tra có cho phép truy cập khi expired không
func (es ExpirationStrategy) AllowsAccess() bool {
	return es == ExpirationMaintainAccess || es == ExpirationAllowAccess
}

// String trả về string representation
func (es ExpirationStrategy) String() string {
	return string(es)
}

// ExpirationBasis - Cơ sở tính toán thời điểm bắt đầu đếm ngược thời hạn license
//
// License có duration (ví dụ: 365 ngày), nhưng bắt đầu đếm từ khi nào?
// Basis này quyết định "mốc thời gian zero" để tính expiry date.
//
// Business scenarios:
// - FROM_CREATION: Bán license theo calendar year (1/1 → 31/12)
// - FROM_FIRST_VALIDATION: Grace period - license chỉ "chạy" khi user bắt đầu dùng
// - FROM_FIRST_ACTIVATION: Tương tự validation nhưng khi activate machine đầu tiên
// - FROM_FIRST_DOWNLOAD: Software distribution - đếm từ khi download lần đầu
// - FROM_FIRST_USE: Flexible - đếm từ khi thực sự sử dụng feature
//
// Ví dụ: License 30 ngày, tạo ngày 1/1/2024
// - FROM_CREATION: Hết hạn 31/1/2024 (dù chưa dùng)
// - FROM_FIRST_VALIDATION: Hết hạn 30 ngày sau lần validate đầu tiên
// - FROM_FIRST_ACTIVATION: Hết hạn 30 ngày sau lần activate machine đầu tiên
// - FROM_FIRST_USE: Hết hạn 30 ngày sau lần sử dụng đầu tiên
type ExpirationBasis string

const (
	ExpirationFromCreation        ExpirationBasis = "FROM_CREATION"         // Đếm từ khi tạo license (default)
	ExpirationFromFirstValidation ExpirationBasis = "FROM_FIRST_VALIDATION" // Đếm từ lần validate đầu tiên
	ExpirationFromFirstActivation ExpirationBasis = "FROM_FIRST_ACTIVATION" // Đếm từ lần activate machine đầu tiên
	ExpirationFromFirstDownload   ExpirationBasis = "FROM_FIRST_DOWNLOAD"   // Đếm từ lần download đầu tiên
	ExpirationFromFirstUse        ExpirationBasis = "FROM_FIRST_USE"        // Đếm từ lần sử dụng đầu tiên
)

// IsValid kiểm tra basis có hợp lệ không
func (eb ExpirationBasis) IsValid() bool {
	switch eb {
	case ExpirationFromCreation, ExpirationFromFirstValidation, ExpirationFromFirstActivation,
		ExpirationFromFirstDownload, ExpirationFromFirstUse:
		return true
	}
	return false
}

// String trả về string representation
func (eb ExpirationBasis) String() string {
	return string(eb)
}

// RenewalBasis - Cơ sở tính toán thời điểm bắt đầu cho chu kỳ license mới khi renew
//
// Khi customer renew license (gia hạn), thời hạn mới sẽ được tính từ khi nào?
// Basis này ảnh hưởng đến việc customer có "mất" thời gian chưa sử dụng hay không.
//
// Business scenarios:
// - FROM_EXPIRY: Công bằng - thời gian mới cộng dồn từ expiry date cũ
// - FROM_NOW: Đơn giản - thời gian mới bắt đầu từ thời điểm renew
// - FROM_NOW_IF_EXPIRED: Hybrid - nếu đã hết hạn thì từ now, chưa hết hạn thì từ expiry
//
// Ví dụ: License hết hạn 31/12/2024, renew 30 ngày vào 15/12/2024
// - FROM_EXPIRY: Hết hạn mới = 31/12/2024 + 30 ngày = 30/1/2025 (không mất 16 ngày)
// - FROM_NOW: Hết hạn mới = 15/12/2024 + 30 ngày = 14/1/2025 (mất 16 ngày chưa dùng)
// - FROM_NOW_IF_EXPIRED: Vì chưa hết hạn → như FROM_EXPIRY = 30/1/2025
//
// Ví dụ 2: License hết hạn 31/12/2024, renew 30 ngày vào 5/1/2025 (đã hết hạn)
// - FROM_EXPIRY: Hết hạn mới = 31/12/2024 + 30 ngày = 30/1/2025 (có thể âm)
// - FROM_NOW: Hết hạn mới = 5/1/2025 + 30 ngày = 4/2/2025
// - FROM_NOW_IF_EXPIRED: Vì đã hết hạn → như FROM_NOW = 4/2/2025
type RenewalBasis string

const (
	RenewalFromExpiry       RenewalBasis = "FROM_EXPIRY"         // Cộng dồn từ expiry date cũ (default)
	RenewalFromNow          RenewalBasis = "FROM_NOW"            // Bắt đầu từ thời điểm renew
	RenewalFromNowIfExpired RenewalBasis = "FROM_NOW_IF_EXPIRED" // Hybrid: từ expiry nếu chưa hết hạn, từ now nếu đã hết hạn
)

// IsValid kiểm tra basis có hợp lệ không
func (rb RenewalBasis) IsValid() bool {
	switch rb {
	case RenewalFromExpiry, RenewalFromNow, RenewalFromNowIfExpired:
		return true
	}
	return false
}

// String trả về string representation
func (rb RenewalBasis) String() string {
	return string(rb)
}

// TransferStrategy - Chiến lược xử lý expiry date khi transfer license sang user/organization khác
//
// Khi license được transfer (chuyển nhượng) từ user A sang user B,
// expiry date của license sẽ được xử lý như thế nào?
//
// Business scenarios:
// - KEEP_EXPIRY: Bảo toàn thời hạn - user B nhận license với thời hạn còn lại của user A
// - RESET_EXPIRY: Reset thời hạn - user B nhận license "như mới" với full duration
//
// Use cases:
// - KEEP_EXPIRY: Transfer bình thường, reseller, internal company transfer
// - RESET_EXPIRY: Promotional transfer, upgrade incentive, customer service recovery
//
// Ví dụ: License 1 năm, tạo 1/1/2024, hết hạn 31/12/2024
// Transfer vào 1/7/2024 (còn 6 tháng):
// - KEEP_EXPIRY: User B nhận license hết hạn 31/12/2024 (còn 6 tháng)
// - RESET_EXPIRY: User B nhận license hết hạn 31/12/2025 (full 1 năm mới)
type TransferStrategy string

const (
	TransferResetExpiry TransferStrategy = "RESET_EXPIRY" // Reset về full duration cho user mới
	TransferKeepExpiry  TransferStrategy = "KEEP_EXPIRY"  // Giữ nguyên expiry date hiện tại (default)
)

// IsValid kiểm tra strategy có hợp lệ không
func (ts TransferStrategy) IsValid() bool {
	switch ts {
	case TransferResetExpiry, TransferKeepExpiry:
		return true
	}
	return false
}

// ResetsExpiry kiểm tra có reset expiry khi transfer không
func (ts TransferStrategy) ResetsExpiry() bool {
	return ts == TransferResetExpiry
}

// String trả về string representation
func (ts TransferStrategy) String() string {
	return string(ts)
}

// AuthenticationStrategy - Chiến lược xác thực khi validate license
//
// Quyết định client phải cung cấp thông tin gì để chứng minh quyền sử dụng license:
// - TOKEN: Sử dụng API token (admin/user token) để xác thực
// - LICENSE: Chỉ cần license key, không cần token (public validation)
// - SESSION: Sử dụng session-based authentication
// - MIXED: Hỗ trợ nhiều phương thức xác thực
// - NONE: Không yêu cầu xác thực (public license)
//
// Security implications:
// - TOKEN: Bảo mật cao, kiểm soát được ai validate license
// - LICENSE: Bảo mật trung bình, license key có thể bị leak
// - SESSION: Phù hợp web applications với session management
// - MIXED: Linh hoạt nhưng phức tạp hơn
// - NONE: Không bảo mật, phù hợp cho public/open-source software
//
// Use cases:
// - TOKEN: Enterprise software, B2B applications
// - LICENSE: Desktop software, offline applications
// - SESSION: Web applications, SaaS platforms
// - MIXED: Multi-platform software với nhiều client types
// - NONE: Free software với license tracking đơn giản
type AuthenticationStrategy string

const (
	AuthToken   AuthenticationStrategy = "TOKEN"   // Yêu cầu API token để validate (default)
	AuthLicense AuthenticationStrategy = "LICENSE" // Chỉ cần license key để validate
	AuthSession AuthenticationStrategy = "SESSION" // Yêu cầu session authentication
	AuthMixed   AuthenticationStrategy = "MIXED"   // Hỗ trợ nhiều phương thức xác thực
	AuthNone    AuthenticationStrategy = "NONE"    // Không yêu cầu xác thực
)

// IsValid kiểm tra strategy có hợp lệ không
func (as AuthenticationStrategy) IsValid() bool {
	switch as {
	case AuthToken, AuthLicense, AuthSession, AuthMixed, AuthNone:
		return true
	}
	return false
}

// SupportsToken kiểm tra có hỗ trợ token auth không
func (as AuthenticationStrategy) SupportsToken() bool {
	return as == AuthToken || as == AuthMixed
}

// SupportsLicense kiểm tra có hỗ trợ license auth không
func (as AuthenticationStrategy) SupportsLicense() bool {
	return as == AuthLicense || as == AuthMixed
}

// SupportsSession kiểm tra có hỗ trợ session auth không
func (as AuthenticationStrategy) SupportsSession() bool {
	return as == AuthSession || as == AuthMixed
}

// RequiresAuth kiểm tra có yêu cầu xác thực không
func (as AuthenticationStrategy) RequiresAuth() bool {
	return as != AuthNone
}

// String trả về string representation
func (as AuthenticationStrategy) String() string {
	return string(as)
}

// OverageStrategy - Chiến lược xử lý khi vượt quá giới hạn tài nguyên của license
//
// Khi license có giới hạn tài nguyên (max_machines, max_cores, max_users) nhưng user cố gắng
// sử dụng vượt quá giới hạn đó, overage strategy quyết định hệ thống sẽ phản ứng như thế nào:
//
// Ví dụ thực tế:
// - License cho phép tối đa 5 máy (max_machines=5)
// - User cố gắng kích hoạt máy thứ 6
// - Overage strategy quyết định: cho phép hay từ chối?
//
// Use cases:
// - Doanh nghiệp cần linh hoạt tạm thời (peak season, emergency scaling)
// - Tránh gián đoạn dịch vụ khi có nhu cầu đột xuất
// - Tạo grace period trước khi yêu cầu upgrade license
// - Soft limits vs hard limits cho user experience tốt hơn
type OverageStrategy string

const (
	OverageAlwaysAllow OverageStrategy = "ALWAYS_ALLOW_OVERAGE" // Cho phép không giới hạn (5 máy → ∞ máy)
	OverageAllow125X   OverageStrategy = "ALLOW_1_25X_OVERAGE"  // Cho phép vượt 25% (5 máy → 6 máy)
	OverageAllow15X    OverageStrategy = "ALLOW_1_5X_OVERAGE"   // Cho phép vượt 50% (5 máy → 7 máy)
	OverageAllow2X     OverageStrategy = "ALLOW_2X_OVERAGE"     // Cho phép vượt 100% (5 máy → 10 máy)
	OverageNoOverage   OverageStrategy = "NO_OVERAGE"           // Từ chối nghiêm ngặt - không cho phép vượt quá
)

// IsValid kiểm tra strategy có hợp lệ không
func (os OverageStrategy) IsValid() bool {
	switch os {
	case OverageAlwaysAllow, OverageAllow125X, OverageAllow15X, OverageAllow2X, OverageNoOverage:
		return true
	}
	return false
}

// AllowsOverage kiểm tra có cho phép vượt quá không
func (os OverageStrategy) AllowsOverage() bool {
	return os != OverageNoOverage
}

// Note: IsConcurrent() method removed - Go implementation uses overage_strategy directly

// MaxOverageMultiplier trả về hệ số nhân tối đa cho phép vượt quá giới hạn gốc
//
// Ví dụ: License có max_machines=5
// - NO_OVERAGE: 1.0 → tối đa 5 máy (5 * 1.0 = 5)
// - ALLOW_1_25X_OVERAGE: 1.25 → tối đa 6 máy (5 * 1.25 = 6.25 ≈ 6)
// - ALLOW_1_5X_OVERAGE: 1.5 → tối đa 7 máy (5 * 1.5 = 7.5 ≈ 7)
// - ALLOW_2X_OVERAGE: 2.0 → tối đa 10 máy (5 * 2.0 = 10)
// - ALWAYS_ALLOW_OVERAGE: -1 → không giới hạn (∞ máy)
func (os OverageStrategy) MaxOverageMultiplier() float64 {
	switch os {
	case OverageAlwaysAllow:
		return -1 // -1 có nghĩa là không giới hạn (unlimited)
	case OverageAllow125X:
		return 1.25 // Cho phép thêm 25% so với giới hạn gốc
	case OverageAllow15X:
		return 1.5 // Cho phép thêm 50% so với giới hạn gốc
	case OverageAllow2X:
		return 2.0 // Cho phép gấp đôi giới hạn gốc
	case OverageNoOverage:
		return 1.0 // NO_OVERAGE: chỉ cho phép đúng giới hạn gốc
	}
	return 1.0
}

// ValidateWithLimit kiểm tra strategy có tương thích với limit không
func (os OverageStrategy) ValidateWithLimit(limit int, isNodeLocked bool) error {
	if isNodeLocked && (os == OverageAllow125X || os == OverageAllow15X) {
		return fmt.Errorf("overage strategy %s không tương thích với node-locked policy", os)
	}

	if os == OverageAllow125X && limit%4 != 0 {
		return fmt.Errorf("overage strategy ALLOW_1_25X_OVERAGE yêu cầu limit chia hết cho 4, nhận được: %d", limit)
	}

	if os == OverageAllow15X && limit%2 != 0 {
		return fmt.Errorf("overage strategy ALLOW_1_5X_OVERAGE yêu cầu limit chia hết cho 2, nhận được: %d", limit)
	}

	return nil
}

// String trả về string representation
func (os OverageStrategy) String() string {
	return string(os)
}

// HeartbeatCullStrategy - Chiến lược xử lý machine/process khi "chết" (không gửi heartbeat)
//
// Heartbeat là tín hiệu "còn sống" mà machine/process phải gửi định kỳ để chứng minh
// nó vẫn đang hoạt động. Khi không nhận được heartbeat trong thời gian quy định,
// machine/process được coi là "chết" và cần xử lý.
//
// Use cases:
// - Phát hiện machine bị crash, mất mạng, hoặc bị tắt đột ngột
// - Giải phóng license slot cho machine khác sử dụng
// - Ngăn chặn "zombie" machines chiếm giữ license vô thời hạn
// - Quản lý tài nguyên license hiệu quả
//
// Ví dụ: License cho phép 5 máy, máy thứ 6 muốn kích hoạt nhưng 1 máy cũ đã "chết"
// - DEACTIVATE_DEAD: Tự động deactivate máy chết → máy thứ 6 có thể kích hoạt
// - KEEP_DEAD: Giữ máy chết trong danh sách → máy thứ 6 bị từ chối
type HeartbeatCullStrategy string

const (
	HeartbeatDeactivateDead HeartbeatCullStrategy = "DEACTIVATE_DEAD" // Tự động deactivate machine chết (default)
	HeartbeatKeepDead       HeartbeatCullStrategy = "KEEP_DEAD"       // Giữ machine chết trong danh sách
)

// IsValid kiểm tra strategy có hợp lệ không
func (hcs HeartbeatCullStrategy) IsValid() bool {
	switch hcs {
	case HeartbeatDeactivateDead, HeartbeatKeepDead:
		return true
	}
	return false
}

// DeactivatesDead kiểm tra có deactivate machine chết không
func (hcs HeartbeatCullStrategy) DeactivatesDead() bool {
	return hcs == HeartbeatDeactivateDead
}

// String trả về string representation
func (hcs HeartbeatCullStrategy) String() string {
	return string(hcs)
}

// HeartbeatResurrectionStrategy - Chiến lược "hồi sinh" machine đã bị coi là chết
//
// Khi machine bị coi là "chết" (không gửi heartbeat), có thể nó chỉ tạm thời mất mạng
// hoặc bị suspend. Strategy này quyết định có cho phép machine "sống lại" không và trong bao lâu.
//
// "Lazarus effect" - machine chết rồi sống lại:
// - Machine A ngừng gửi heartbeat → bị đánh dấu "chết"
// - Machine B kích hoạt license (sử dụng slot của A)
// - Machine A đột nhiên gửi heartbeat lại → conflict!
//
// Resurrection strategy giải quyết conflict này:
// - NO_REVIVE: Machine A không được phép sống lại, phải deactivate và activate lại
// - Time-based: Machine A có thể sống lại trong khoảng thời gian nhất định
// - ALWAYS_REVIVE: Machine A luôn được phép sống lại (có thể gây conflict)
//
// Use cases:
// - NO_REVIVE: Strict licensing, tránh conflicts
// - Time-based: Balance giữa user experience và license compliance
// - ALWAYS_REVIVE: Flexible licensing, ưu tiên user experience
//
// Ví dụ: Machine A "chết", Machine B activate, sau 3 phút Machine A ping lại
// - NO_REVIVE: Machine A bị từ chối, phải deactivate B rồi activate A
// - 5_MINUTE_REVIVE: Machine A được phép sống lại (trong 5 phút)
// - 1_MINUTE_REVIVE: Machine A bị từ chối (quá 1 phút)
type HeartbeatResurrectionStrategy string

const (
	HeartbeatAlwaysRevive    HeartbeatResurrectionStrategy = "ALWAYS_REVIVE"    // Luôn cho phép hồi sinh
	HeartbeatRevive15Minutes HeartbeatResurrectionStrategy = "15_MINUTE_REVIVE" // Cho phép hồi sinh trong 15 phút
	HeartbeatRevive10Minutes HeartbeatResurrectionStrategy = "10_MINUTE_REVIVE" // Cho phép hồi sinh trong 10 phút
	HeartbeatRevive5Minutes  HeartbeatResurrectionStrategy = "5_MINUTE_REVIVE"  // Cho phép hồi sinh trong 5 phút
	HeartbeatRevive2Minutes  HeartbeatResurrectionStrategy = "2_MINUTE_REVIVE"  // Cho phép hồi sinh trong 2 phút
	HeartbeatRevive1Minute   HeartbeatResurrectionStrategy = "1_MINUTE_REVIVE"  // Cho phép hồi sinh trong 1 phút
	HeartbeatNoRevive        HeartbeatResurrectionStrategy = "NO_REVIVE"        // Không cho phép hồi sinh (default)
)

// IsValid kiểm tra strategy có hợp lệ không
func (hrs HeartbeatResurrectionStrategy) IsValid() bool {
	switch hrs {
	case HeartbeatAlwaysRevive, HeartbeatRevive15Minutes, HeartbeatRevive10Minutes,
		HeartbeatRevive5Minutes, HeartbeatRevive2Minutes, HeartbeatRevive1Minute, HeartbeatNoRevive:
		return true
	}
	return false
}

// ResurrectsDead kiểm tra có hồi sinh machine chết không
func (hrs HeartbeatResurrectionStrategy) ResurrectsDead() bool {
	return hrs != HeartbeatNoRevive
}

// AlwaysResurrects kiểm tra có luôn hồi sinh không
func (hrs HeartbeatResurrectionStrategy) AlwaysResurrects() bool {
	return hrs == HeartbeatAlwaysRevive
}

// GetTTLSeconds trả về thời gian TTL (Time To Live) cho lazarus resurrection
//
// TTL là khoảng thời gian (tính bằng giây) mà machine "chết" vẫn có thể "sống lại".
// Sau khi vượt quá TTL, machine phải deactivate và activate lại từ đầu.
//
// Return values:
// - Số dương: TTL tính bằng giây (60 = 1 phút, 900 = 15 phút)
// - 86400: 24 giờ cho ALWAYS_REVIVE (practical limit thay vì unlimited)
// - 0: No resurrection (machine không thể sống lại)
//
// Use case: Machine A "chết" lúc 10:00, TTL = 300 giây (5 phút)
// - 10:04: Machine A ping → được phép sống lại (trong TTL)
// - 10:06: Machine A ping → bị từ chối (quá TTL)
func (hrs HeartbeatResurrectionStrategy) GetTTLSeconds() int {
	switch hrs {
	case HeartbeatRevive15Minutes:
		return 15 * 60 // 900 seconds = 15 minutes
	case HeartbeatRevive10Minutes:
		return 10 * 60 // 600 seconds = 10 minutes
	case HeartbeatRevive5Minutes:
		return 5 * 60 // 300 seconds = 5 minutes
	case HeartbeatRevive2Minutes:
		return 2 * 60 // 120 seconds = 2 minutes
	case HeartbeatRevive1Minute:
		return 1 * 60 // 60 seconds = 1 minute
	case HeartbeatAlwaysRevive:
		return 24 * 60 * 60 // 86400 seconds = 24 hours (practical unlimited)
	default:
		return 0 // No resurrection allowed
	}
}

// String trả về string representation
func (hrs HeartbeatResurrectionStrategy) String() string {
	return string(hrs)
}

// HeartbeatBasis - Cơ sở tính toán thời điểm bắt đầu yêu cầu heartbeat
//
// Machine cần gửi heartbeat định kỳ để chứng minh vẫn "sống".
// Basis này quyết định khi nào bắt đầu đếm thời gian heartbeat timeout.
//
// Business scenarios:
// - FROM_CREATION: Bắt đầu đếm ngay khi machine được tạo/activate
// - FROM_FIRST_PING: Bắt đầu đếm từ khi machine gửi heartbeat đầu tiên
//
// Use cases:
// - FROM_CREATION: Strict monitoring - machine phải ping ngay sau khi activate
// - FROM_FIRST_PING: Flexible - machine có thể delay việc bắt đầu ping
//
// Ví dụ: Machine activate lúc 10:00, heartbeat duration = 5 phút
// - FROM_CREATION: Machine phải ping trước 10:05, nếu không sẽ bị coi là chết
// - FROM_FIRST_PING: Machine ping lần đầu lúc 10:03, phải ping lần 2 trước 10:08
//
// Impact:
// - FROM_CREATION: Phát hiện machine inactive sớm hơn
// - FROM_FIRST_PING: Cho phép machine có thời gian setup trước khi bắt đầu ping
type HeartbeatBasis string

const (
	HeartbeatFromCreation  HeartbeatBasis = "FROM_CREATION"   // Đếm từ khi machine được tạo (default)
	HeartbeatFromFirstPing HeartbeatBasis = "FROM_FIRST_PING" // Đếm từ lần ping đầu tiên
)

// IsValid kiểm tra basis có hợp lệ không
func (hb HeartbeatBasis) IsValid() bool {
	switch hb {
	case HeartbeatFromCreation, HeartbeatFromFirstPing:
		return true
	}
	return false
}

// String trả về string representation
func (hb HeartbeatBasis) String() string {
	return string(hb)
}

// LeasingStrategy - Chiến lược phân bổ lease (temporary license usage) cho machines/processes
//
// Leasing cho phép machine/process "mượn" license slot tạm thời thay vì activate vĩnh viễn.
// Strategy này quyết định phạm vi chia sẻ lease pool giữa các entities.
//
// Concepts:
// - Machine leasing: Machine "mượn" slot, tự động trả lại khi không dùng
// - Process leasing: Process "mượn" slot, nhiều processes có thể share cùng machine
// - Lease pool: Tập hợp slots có thể được chia sẻ
//
// Business scenarios:
// - PER_LICENSE: Tất cả machines/processes của license chia chung pool
// - PER_USER: Mỗi user có pool riêng, không chia sẻ với user khác
// - PER_MACHINE: Mỗi machine có pool riêng cho processes (chỉ process leasing)
//
// Use cases:
// - PER_LICENSE: Team license - tất cả members chia chung slots
// - PER_USER: Individual license - mỗi user có quota riêng
// - PER_MACHINE: Multi-process software - processes trên cùng máy chia chung
//
// Ví dụ: License 5 slots, 2 users (A, B)
// - PER_LICENSE: A dùng 3 slots, B có thể dùng 2 slots còn lại
// - PER_USER: A có 5 slots riêng, B có 5 slots riêng (total 10 slots)
// - PER_MACHINE: Chỉ áp dụng cho process leasing trong cùng machine
type LeasingStrategy string

const (
	LeasingPerLicense LeasingStrategy = "PER_LICENSE" // Chia chung pool cho toàn license (default)
	LeasingPerUser    LeasingStrategy = "PER_USER"    // Mỗi user có pool riêng
	LeasingPerMachine LeasingStrategy = "PER_MACHINE" // Mỗi machine có pool riêng (chỉ process leasing)
)

// IsValid kiểm tra strategy có hợp lệ không
func (ls LeasingStrategy) IsValid() bool {
	switch ls {
	case LeasingPerLicense, LeasingPerUser, LeasingPerMachine:
		return true
	}
	return false
}

// IsValidForMachine kiểm tra có hợp lệ cho machine leasing không
func (ls LeasingStrategy) IsValidForMachine() bool {
	return ls == LeasingPerLicense || ls == LeasingPerUser
}

// IsValidForProcess kiểm tra có hợp lệ cho process leasing không
func (ls LeasingStrategy) IsValidForProcess() bool {
	return ls == LeasingPerLicense || ls == LeasingPerUser || ls == LeasingPerMachine
}

// String trả về string representation
func (ls LeasingStrategy) String() string {
	return string(ls)
}
