package entities

import (
	"fmt"
	"strings"

	"github.com/google/uuid"
)

// === MACHINE VALIDATION - MAPPING FROM RUBY ===
// Validation logic mapping từ Ruby Machine model validations
// <PERSON><PERSON> gồ<PERSON> tất cả business rules và constraints từ keygen-api

// ValidateBasicFields validates basic machine fields (Ruby: validates)
func (m *Machine) ValidateBasicFields() error {
	// Validate fingerprint (required)
	if strings.TrimSpace(m.Fingerprint) == "" {
		return fmt.Errorf("fingerprint: is required")
	}

	// Validate fingerprint exclusion (reserved aliases)
	excludedAliases := []string{"admin", "api", "www", "ftp", "mail", "email", "support", "help", "billing"}
	for _, alias := range excludedAliases {
		if strings.ToLower(m.Fingerprint) == alias {
			return fmt.Errorf("fingerprint: is reserved")
		}
	}

	// Validate metadata length (max 64 keys)
	if len(m.Metadata) > 64 {
		return fmt.Errorf("metadata: too many keys (exceeded limit of 64 keys)")
	}

	// Validate cores range
	if m.Cores < 1 {
		return fmt.Errorf("cores: must be greater than or equal to 1")
	}
	if m.Cores > ********** {
		return fmt.Errorf("cores: must be less than or equal to **********")
	}

	// Validate max_processes_override range (if present)
	if m.MaxProcessesOverride != nil {
		if *m.MaxProcessesOverride < 0 {
			return fmt.Errorf("max_processes_override: must be greater than or equal to 0")
		}
		if *m.MaxProcessesOverride > ********** {
			return fmt.Errorf("max_processes_override: must be less than or equal to **********")
		}
	}

	return nil
}

// ValidateUUIDFormat validates UUID format for all ID fields (Ruby: validate UUID format)
func (m *Machine) ValidateUUIDFormat() error {
	// Validate ID if present
	if m.ID != "" {
		if _, err := uuid.Parse(m.ID); err != nil {
			return fmt.Errorf("id: must be a valid UUID")
		}
	}

	// Validate LicenseID (required)
	if _, err := uuid.Parse(m.LicenseID); err != nil {
		return fmt.Errorf("license_id: must be a valid UUID")
	}

	// Validate PolicyID (required)
	if _, err := uuid.Parse(m.PolicyID); err != nil {
		return fmt.Errorf("policy_id: must be a valid UUID")
	}

	// Validate OwnerID if present
	if m.OwnerID != nil && *m.OwnerID != "" {
		if _, err := uuid.Parse(*m.OwnerID); err != nil {
			return fmt.Errorf("owner_id: must be a valid UUID")
		}
	}

	return nil
}

// ValidateFingerprintUniqueness validates fingerprint uniqueness (Ruby: validate fingerprint uniqueness)
// Cần repository để check uniqueness trong database
func (m *Machine) ValidateFingerprintUniqueness(existingMachines []Machine) error {
	// Kiểm tra fingerprint không conflict với UUID của machine khác
	for _, existing := range existingMachines {
		if existing.ID == m.Fingerprint {
			return fmt.Errorf("fingerprint: must not conflict with another machine's identifier (UUID)")
		}
	}

	// Kiểm tra uniqueness theo policy strategy
	switch {
	case m.UniquePerLicense():
		// Check uniqueness per license (default)
		for _, existing := range existingMachines {
			if existing.LicenseID == m.LicenseID && existing.Fingerprint == m.Fingerprint && existing.ID != m.ID {
				return fmt.Errorf("fingerprint: has already been taken")
			}
		}
	case m.UniquePerPolicy():
		// Check uniqueness per policy
		for _, existing := range existingMachines {
			if existing.PolicyID == m.PolicyID && existing.Fingerprint == m.Fingerprint && existing.ID != m.ID {
				return fmt.Errorf("fingerprint: has already been taken for this policy")
			}
		}
	case m.UniquePerProduct():
		// Check uniqueness per product (cần product ID)
		// TODO: Implement product-level uniqueness check
		return fmt.Errorf("product-level uniqueness validation not implemented")
	case m.UniquePerAccount():
		// Check uniqueness per account (organization)
		// TODO: Implement account-level uniqueness check
		return fmt.Errorf("account-level uniqueness validation not implemented")
	}

	return nil
}

// ValidateMachineOverage validates machine count overage (Ruby: validate machine overage)
func (m *Machine) ValidateMachineOverage(license *License, existingMachines []Machine) error {
	if license == nil {
		return fmt.Errorf("license is required for overage validation")
	}

	// Skip validation if license allows overage
	if license.AlwaysAllowOverage() {
		return nil
	}

	// Skip if no max machines limit
	maxMachines := license.GetMaxMachines(nil) // nil = policy max_machines
	if maxMachines == nil || *maxMachines <= 0 {
		return nil
	}

	// Count existing machines based on leasing strategy
	var currentCount int
	switch {
	case m.LeasePerLicense():
		// Count all machines for this license
		for _, existing := range existingMachines {
			if existing.LicenseID == m.LicenseID && existing.ID != m.ID {
				currentCount++
			}
		}
	case m.LeasePerUser():
		// Count machines for this user on this license
		for _, existing := range existingMachines {
			if existing.LicenseID == m.LicenseID && existing.OwnerID == m.OwnerID && existing.ID != m.ID {
				currentCount++
			}
		}
	default:
		// Default to per-license counting
		for _, existing := range existingMachines {
			if existing.LicenseID == m.LicenseID && existing.ID != m.ID {
				currentCount++
			}
		}
	}

	// Skip if no existing machines
	if currentCount == 0 {
		return nil
	}

	nextMachineCount := currentCount + 1
	if nextMachineCount <= *maxMachines {
		return nil // Within limit
	}

	// Check overage allowance
	if license.Allow125xOverage() && nextMachineCount <= int(float64(*maxMachines)*1.25) {
		return nil
	}
	if license.Allow15xOverage() && nextMachineCount <= int(float64(*maxMachines)*1.5) {
		return nil
	}
	if license.Allow2xOverage() && nextMachineCount <= (*maxMachines)*2 {
		return nil
	}

	// Overage exceeded
	if m.LeasePerUser() {
		return fmt.Errorf("machine count has exceeded maximum allowed for user (%d)", *maxMachines)
	}
	return fmt.Errorf("machine count has exceeded maximum allowed for license (%d)", *maxMachines)
}

// ValidateCoreOverage validates core count overage (Ruby: validate core overage)
func (m *Machine) ValidateCoreOverage(license *License, existingMachines []Machine) error {
	if license == nil {
		return fmt.Errorf("license is required for core overage validation")
	}

	// Skip validation if license allows overage
	if license.AlwaysAllowOverage() {
		return nil
	}

	// Skip if no max cores limit
	maxCores := license.GetMaxCores(nil) // nil = policy max_cores
	if maxCores == nil || *maxCores <= 0 {
		return nil
	}

	// Count existing cores based on leasing strategy
	var currentCoreCount int
	switch {
	case m.LeasePerLicense():
		// Count all cores for this license (excluding current machine)
		for _, existing := range existingMachines {
			if existing.LicenseID == m.LicenseID && existing.ID != m.ID {
				currentCoreCount += existing.GetCoreCount()
			}
		}
	case m.LeasePerUser():
		// Count cores for this user on this license (excluding current machine)
		for _, existing := range existingMachines {
			if existing.LicenseID == m.LicenseID && existing.OwnerID == m.OwnerID && existing.ID != m.ID {
				currentCoreCount += existing.GetCoreCount()
			}
		}
	default:
		// Default to per-license counting
		for _, existing := range existingMachines {
			if existing.LicenseID == m.LicenseID && existing.ID != m.ID {
				currentCoreCount += existing.GetCoreCount()
			}
		}
	}

	nextCoreCount := currentCoreCount + m.GetCoreCount()
	if nextCoreCount <= *maxCores {
		return nil // Within limit
	}

	// Check overage allowance
	if license.Allow125xOverage() && nextCoreCount <= int(float64(*maxCores)*1.25) {
		return nil
	}
	if license.Allow15xOverage() && nextCoreCount <= int(float64(*maxCores)*1.5) {
		return nil
	}
	if license.Allow2xOverage() && nextCoreCount <= (*maxCores)*2 {
		return nil
	}

	// Core overage exceeded
	if m.LeasePerUser() {
		return fmt.Errorf("machine core count has exceeded maximum allowed for user (%d)", *maxCores)
	}
	return fmt.Errorf("machine core count has exceeded maximum allowed for license (%d)", *maxCores)
}

// ValidateOwnerIsLicenseUser validates owner is a license user (Ruby: validate owner is license user)
func (m *Machine) ValidateOwnerIsLicenseUser(licenseUsers []string) error {
	// Skip if no owner
	if m.OwnerID == nil || *m.OwnerID == "" {
		return nil
	}

	// Check if owner is in license users
	for _, userID := range licenseUsers {
		if userID == *m.OwnerID {
			return nil // Valid
		}
	}

	return fmt.Errorf("owner: must be a valid license user")
}

// SetDefaultValues sets default values for machine (Ruby: before_create callbacks)
func (m *Machine) SetDefaultValues() {
	// Set default status
	if m.Status == "" {
		m.Status = MachineStatusActive
	}

	// Generate fingerprint if empty and has components
	if m.Fingerprint == "" && len(m.Components) > 0 {
		m.Fingerprint = m.GenerateFingerprint()
	}

	// Initialize metadata if nil
	if m.Metadata == nil {
		m.Metadata = make(Metadata)
	}

	// Initialize components if nil
	if m.Components == nil {
		m.Components = make(MachineComponents)
	}
}
