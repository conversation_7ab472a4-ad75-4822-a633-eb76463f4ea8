package entities

import (
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
)

// ===== LICENSE VALIDATION METHODS - GOLANG STYLE =====
// Mapping từ Ruby License model validations với Go type safety
// Bỏ group và environment validations, đổi account thành organization

var (
	// Excluded license key aliases (Ruby: EXCLUDED_ALIASES)
	excludedAliases = []string{
		"admin", "api", "www", "ftp", "mail", "email", "support", "help",
		"billing", "account", "accounts", "user", "users", "license", "licenses",
		"key", "keys", "token", "tokens", "auth", "authentication", "login",
		"signup", "register", "dashboard", "app", "application", "service",
		"services", "product", "products", "policy", "policies", "machine",
		"machines", "device", "devices", "client", "clients", "server",
		"servers", "host", "hosts", "node", "nodes", "endpoint", "endpoints",
	}
)

// ValidateBasicFields validates basic license fields (Ruby: validates)
func (l *License) ValidateBasicFields() error {
	// Validate metadata length (Ruby: validates :metadata, length: { maximum: 64 })
	if l.Metadata != nil && len(l.Metadata) > 64 {
		return fmt.Errorf("metadata: too many keys (exceeded limit of 64 keys)")
	}

	// Validate uses (Ruby: validates :uses, numericality: { greater_than_or_equal_to: 0 })
	if l.Uses < 0 {
		return fmt.Errorf("uses: must be greater than or equal to 0")
	}

	return nil
}

// ValidateKeyOnCreate validates license key on creation (Ruby: validates :key on: :create)
func (l *License) ValidateKeyOnCreate() error {
	if l.Key == "" {
		return nil // Key will be auto-generated
	}

	// Check excluded aliases (Ruby: exclusion: { in: EXCLUDED_ALIASES })
	for _, alias := range excludedAliases {
		if strings.EqualFold(l.Key, alias) {
			return fmt.Errorf("key: is reserved")
		}
	}

	// Key length validation (Ruby: length: { minimum: 1, maximum: 100.kilobytes })
	if len(l.Key) < 1 {
		return fmt.Errorf("key: is too short (minimum is 1 character)")
	}
	if len(l.Key) > 100*1024 { // 100KB
		return fmt.Errorf("key: is too long (maximum is 100KB)")
	}

	// Non-crypted keys minimum length (Ruby: length: { minimum: 6 } if !scheme?)
	// Note: Go implementation doesn't have scheme field in License, delegated to Policy
	if len(l.Key) < 6 {
		return fmt.Errorf("key: is too short (minimum is 6 characters for non-crypted keys)")
	}

	return nil
}

// ValidateOverrideLimits validates policy override limits (Ruby: validates max_* fields)
func (l *License) ValidateOverrideLimits() error {
	const maxInt32 = **********

	// Validate max_machines_override
	if l.MaxMachinesOverride != nil {
		if *l.MaxMachinesOverride < 0 || *l.MaxMachinesOverride > maxInt32 {
			return fmt.Errorf("max_machines_override: must be between 0 and %d", maxInt32)
		}
	}

	// Validate max_cores_override
	if l.MaxCoresOverride != nil {
		if *l.MaxCoresOverride < 1 || *l.MaxCoresOverride > maxInt32 {
			return fmt.Errorf("max_cores_override: must be between 1 and %d", maxInt32)
		}
	}

	// Validate max_uses_override
	if l.MaxUsesOverride != nil {
		if *l.MaxUsesOverride < 0 || *l.MaxUsesOverride > maxInt32 {
			return fmt.Errorf("max_uses_override: must be between 0 and %d", maxInt32)
		}
	}

	// Validate max_processes_override
	if l.MaxProcessesOverride != nil {
		if *l.MaxProcessesOverride < 0 || *l.MaxProcessesOverride > maxInt32 {
			return fmt.Errorf("max_processes_override: must be between 0 and %d", maxInt32)
		}
	}

	// Validate max_users_override
	if l.MaxUsersOverride != nil {
		if *l.MaxUsersOverride < 0 || *l.MaxUsersOverride > maxInt32 {
			return fmt.Errorf("max_users_override: must be between 0 and %d", maxInt32)
		}
	}

	return nil
}

// ValidateUsageLimit validates usage doesn't exceed max_uses (Ruby: validate on: :update)
func (l *License) ValidateUsageLimit(maxUses *int) error {
	if maxUses == nil {
		return nil
	}

	if l.Uses > *maxUses {
		return fmt.Errorf("uses: usage exceeds maximum allowed for license (%d)", *maxUses)
	}

	return nil
}

// ValidateUUIDFormat validates UUID format using google/uuid package
func (l *License) ValidateUUIDFormat() error {
	// Validate ID if present
	if l.ID != "" {
		if _, err := uuid.Parse(l.ID); err != nil {
			return fmt.Errorf("id: must be a valid UUID")
		}
	}

	// Validate UserID if present
	if l.UserID != nil && *l.UserID != "" {
		if _, err := uuid.Parse(*l.UserID); err != nil {
			return fmt.Errorf("user_id: must be a valid UUID")
		}
	}

	// Validate OrganizationID (required)
	if _, err := uuid.Parse(l.OrganizationID); err != nil {
		return fmt.Errorf("organization_id: must be a valid UUID")
	}

	// Validate ProductID (required)
	if _, err := uuid.Parse(l.ProductID); err != nil {
		return fmt.Errorf("product_id: must be a valid UUID")
	}

	// Validate PolicyID (required)
	if _, err := uuid.Parse(l.PolicyID); err != nil {
		return fmt.Errorf("policy_id: must be a valid UUID")
	}

	return nil
}

// SetDefaultValues sets default values like Ruby before_create callbacks
// Simplified version without group/environment logic
func (l *License) SetDefaultValues() {
	now := time.Now()

	if l.CreatedAt.IsZero() {
		l.CreatedAt = now
	}

	if l.UpdatedAt.IsZero() {
		l.UpdatedAt = now
	}

	// Note: protected value will be set from policy in handler
	// Note: expiry will be set based on policy duration in handler
	// Note: key auto-generation handled in handler
}

// NOTE: Database-dependent validations (key uniqueness, owner existence, policy compatibility)
// are implemented in the service layer, not in entity layer following hexagon architecture principles.
// These validations require repository access and should be handled by:
// - LicenseService.CreateLicense() for creation validations
// - LicenseService.UpdateLicense() for update validations
// - Repository layer for uniqueness constraints
