package entities

import (
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// License đại diện cho một license key với tất cả metadata và rules
// Trong GoKeys, license là core entity chứa license key và inherit rules từ policy
// License có thể override policy limits và track usage/validation
// Simplified từ Ruby License model - bỏ group và environment, đổi account thành organization
type License struct {
	// === CORE IDENTIFIERS ===
	// Các trường định danh cơ bản
	ID             string  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"` // UUID của license
	OrganizationID string  `json:"organization_id" gorm:"type:uuid;not null"`                 // Organization sở hữu license (Ruby: account_id)
	ProductID      string  `json:"product_id" gorm:"type:uuid;not null"`                      // Product mà license thuộc về
	PolicyID       string  `json:"policy_id" gorm:"type:uuid;not null"`                       // Policy chứa rules cho license
	Key            string  `json:"key" gorm:"size:255;not null;uniqueIndex:idx_org_key"`      // License key (unique per organization)
	Name           *string `json:"name,omitempty" gorm:"size:255"`                            // Tên license do user đặt (optional)

	// === LICENSE OWNERSHIP ===
	// Simplified ownership - chỉ support user ownership như Ruby (bỏ polymorphic)
	UserID *string `json:"user_id,omitempty" gorm:"type:uuid"` // User sở hữu license (Ruby: user_id, optional)

	// === LICENSE STATE ===
	// Trạng thái và lifecycle của license (Ruby tính computed, Go store explicit)
	Suspended bool `json:"suspended" gorm:"default:false"` // License có bị suspend không
	Protected bool `json:"protected" gorm:"default:false"` // License có được bảo vệ không

	// === USAGE TRACKING ===
	// Theo dõi việc sử dụng license
	Uses   int        `json:"uses" gorm:"default:0"` // Số lần đã sử dụng license
	Expiry *time.Time `json:"expiry,omitempty"`      // Thời điểm hết hạn (Ruby: expiry field)

	// === POLICY OVERRIDES ===
	// Cho phép override policy limits per license (flexibility)
	MaxUsesOverride      *int `json:"max_uses_override,omitempty"`      // Override số lần sử dụng tối đa
	MaxMachinesOverride  *int `json:"max_machines_override,omitempty"`  // Override số máy tối đa
	MaxCoresOverride     *int `json:"max_cores_override,omitempty"`     // Override số cores tối đa
	MaxUsersOverride     *int `json:"max_users_override,omitempty"`     // Override số users tối đa
	MaxProcessesOverride *int `json:"max_processes_override,omitempty"` // Override số processes tối đa

	// === CACHED COUNTS ===
	// Cache các counts để performance (tránh expensive queries)
	MachinesCount     int `json:"machines_count" gorm:"default:0"`      // Số machines hiện tại
	MachinesCoreCount int `json:"machines_core_count" gorm:"default:0"` // Tổng số cores đang sử dụng
	LicenseUsersCount int `json:"license_users_count" gorm:"default:0"` // Số users đang sử dụng

	// === HEARTBEAT & VALIDATION TRACKING ===
	// Theo dõi check-in, validation và checkout activities
	LastCheckInAt         *time.Time `json:"last_check_in_at"`        // Lần cuối check-in
	LastValidatedAt       *time.Time `json:"last_validated_at"`       // Lần cuối validate license
	LastValidatedChecksum *string    `json:"last_validated_checksum"` // Checksum của lần validate cuối
	LastValidatedVersion  *string    `json:"last_validated_version"`  // Version của lần validate cuối
	LastCheckOutAt        *time.Time `json:"last_check_out_at"`       // Lần cuối check-out (floating licenses)

	// === EVENT TRACKING ===
	// Theo dõi các events đã gửi để tránh spam notifications
	LastExpirationEventSentAt   *time.Time `json:"last_expiration_event_sent_at"`    // Lần cuối gửi expiration event
	LastCheckInEventSentAt      *time.Time `json:"last_check_in_event_sent_at"`      // Lần cuối gửi check-in event
	LastExpiringSoonEventSentAt *time.Time `json:"last_expiring_soon_event_sent_at"` // Lần cuối gửi expiring soon event
	LastCheckInSoonEventSentAt  *time.Time `json:"last_check_in_soon_event_sent_at"` // Lần cuối gửi check-in soon event

	// === FLEXIBLE DATA ===
	// Custom metadata key-value cho license
	Metadata Metadata `json:"metadata" gorm:"type:jsonb;default:'{}'"` // Custom metadata JSONB

	// === AUDIT FIELDS ===
	// Các trường audit chuẩn cho tracking changes
	CreatedAt time.Time      `json:"created_at" gorm:"not null"`        // Thời điểm tạo license
	UpdatedAt time.Time      `json:"updated_at" gorm:"not null"`        // Thời điểm cập nhật cuối
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"` // Soft delete timestamp

	// === RELATIONSHIPS ===
	// Các mối quan hệ với entities khác (lazy loading)
	Organization Organization `json:"organization,omitempty" gorm:"foreignKey:OrganizationID"` // Organization sở hữu license
	Product      Product      `json:"product,omitempty" gorm:"foreignKey:ProductID"`           // Product mà license thuộc về
	Policy       Policy       `json:"policy,omitempty" gorm:"foreignKey:PolicyID"`             // Policy chứa rules cho license
	Machines     []Machine    `json:"machines,omitempty" gorm:"foreignKey:LicenseID"`          // Danh sách machines sử dụng license
}

// TableName override tên table cho GORM
// Đảm bảo GORM sử dụng đúng tên table "licenses" trong database
func (License) TableName() string {
	return "licenses"
}

// ===== LICENSE STATUS METHODS - MAPPING FROM RUBY =====

// License status constants (Go enum pattern)
const (
	LicenseStatusSuspended = "SUSPENDED"
	LicenseStatusExpired   = "EXPIRED"
	LicenseStatusExpiring  = "EXPIRING"
	LicenseStatusInactive  = "INACTIVE"
	LicenseStatusActive    = "ACTIVE"
)

// HasOwner checks if the license has an owner (user)
func (l *License) HasOwner() bool {
	return l.UserID != nil && *l.UserID != ""
}

// IsExpired checks if the license has expired (Ruby: expired?)
func (l *License) IsExpired() bool {
	return l.Expiry != nil && time.Now().After(*l.Expiry)
}

// IsExpiring checks if license expires within 3 days (Ruby: expiring?)
func (l *License) IsExpiring() bool {
	if l.Expiry == nil {
		return false
	}
	now := time.Now()
	return l.Expiry.After(now) && l.Expiry.Before(now.Add(72*time.Hour)) // 3 days
}

// IsSuspended checks if license is suspended (Ruby: suspended?)
func (l *License) IsSuspended() bool {
	return l.Suspended
}

// IsActive checks if license is active (Ruby: active?)
// Active means created/validated/checked within 90 days
func (l *License) IsActive() bool {
	threshold := time.Now().Add(-90 * 24 * time.Hour)

	// Check any recent activity (Go style - early return pattern)
	if l.CreatedAt.After(threshold) {
		return true
	}
	if l.LastValidatedAt != nil && l.LastValidatedAt.After(threshold) {
		return true
	}
	if l.LastCheckOutAt != nil && l.LastCheckOutAt.After(threshold) {
		return true
	}
	if l.LastCheckInAt != nil && l.LastCheckInAt.After(threshold) {
		return true
	}

	return false
}

// IsInactive checks if license is inactive (Ruby: inactive?)
func (l *License) IsInactive() bool {
	return !l.IsActive()
}

// Status returns computed status like Ruby (Ruby: status method)
// Uses priority order: suspended > expired > expiring > inactive > active
func (l *License) Status() string {
	switch {
	case l.IsSuspended():
		return LicenseStatusSuspended
	case l.IsExpired():
		return LicenseStatusExpired
	case l.IsExpiring():
		return LicenseStatusExpiring
	case l.IsInactive():
		return LicenseStatusInactive
	default:
		return LicenseStatusActive
	}
}

// ===== LICENSE BUSINESS ACTIONS - MAPPING FROM RUBY =====

// Suspend suspends the license (Ruby: suspend!)
func (l *License) Suspend() {
	if !l.Suspended { // Only update if state changes
		l.Suspended = true
		l.UpdatedAt = time.Now()
	}
}

// Reinstate reinstates the license (Ruby: reinstate!)
func (l *License) Reinstate() {
	if l.Suspended { // Only update if state changes
		l.Suspended = false
		l.UpdatedAt = time.Now()
	}
}

// CheckIn updates last check-in timestamp (Ruby: check_in!)
func (l *License) CheckIn() {
	now := time.Now()
	l.LastCheckInAt = &now
	l.UpdatedAt = now
}

// IncrementUsage increments usage counter (Ruby: uses increment)
func (l *License) IncrementUsage() {
	l.Uses++
	l.UpdatedAt = time.Now()
}

// SetExpiry sets expiry date (Ruby: expiry= methods)
func (l *License) SetExpiry(expiry *time.Time) {
	l.Expiry = expiry
	l.UpdatedAt = time.Now()
}

// ===== LICENSE LIMIT METHODS - MAPPING FROM RUBY =====
// These methods implement Ruby's override pattern: license override takes precedence over policy default

// GetMaxMachines returns effective max machines (override or policy default)
// Ruby: def max_machines = max_machines_override? ? max_machines_override : policy&.max_machines
func (l *License) GetMaxMachines(policyDefault *int) *int {
	return l.getEffectiveLimit(l.MaxMachinesOverride, policyDefault)
}

// GetMaxCores returns effective max cores (override or policy default)
// Ruby: def max_cores = max_cores_override? ? max_cores_override : policy&.max_cores
func (l *License) GetMaxCores(policyDefault *int) *int {
	return l.getEffectiveLimit(l.MaxCoresOverride, policyDefault)
}

// GetMaxUses returns effective max uses (override or policy default)
// Ruby: def max_uses = max_uses_override? ? max_uses_override : policy&.max_uses
func (l *License) GetMaxUses(policyDefault *int) *int {
	return l.getEffectiveLimit(l.MaxUsesOverride, policyDefault)
}

// GetMaxProcesses returns effective max processes (override or policy default)
// Ruby: def max_processes = max_processes_override? ? max_processes_override : policy&.max_processes
func (l *License) GetMaxProcesses(policyDefault *int) *int {
	return l.getEffectiveLimit(l.MaxProcessesOverride, policyDefault)
}

// GetMaxUsers returns effective max users (override or policy default)
// Ruby: def max_users = max_users_override? ? max_users_override : policy&.max_users
func (l *License) GetMaxUsers(policyDefault *int) *int {
	return l.getEffectiveLimit(l.MaxUsersOverride, policyDefault)
}

// getEffectiveLimit is a helper method to implement Ruby's override pattern (DRY principle)
func (l *License) getEffectiveLimit(override, policyDefault *int) *int {
	if override != nil {
		return override
	}
	return policyDefault
}

// GetUsersCount returns total users count including owner
// Ruby: def users_count = license_users_count + (owner_id? ? 1 : 0)
func (l *License) GetUsersCount() int {
	count := l.LicenseUsersCount
	if l.HasOwner() {
		count++
	}
	return count
}

// ===== LICENSE EXPIRY BUSINESS LOGIC - MAPPING FROM RUBY =====

// SetExpiryOnCreation sets expiry based on policy duration (Ruby: set_expiry_on_creation)
func (l *License) SetExpiryOnCreation(policyDuration *int64) {
	if l.Expiry != nil || policyDuration == nil {
		return // Already has expiry or no duration
	}

	duration := time.Duration(*policyDuration) * time.Second
	expiry := time.Now().Add(duration)
	l.Expiry = &expiry
}

// SetExpiryOnFirstValidation sets expiry on first validation (Ruby: set_expiry_on_first_validation!)
func (l *License) SetExpiryOnFirstValidation(policyDuration *int64) {
	if l.Expiry != nil || policyDuration == nil {
		return // Already has expiry or no duration
	}

	duration := time.Duration(*policyDuration) * time.Second
	expiry := time.Now().Add(duration)
	l.Expiry = &expiry
}

// SetExpiryOnFirstActivation sets expiry on first machine activation (Ruby: set_expiry_on_first_activation!)
func (l *License) SetExpiryOnFirstActivation(policyDuration *int64) {
	if l.Expiry != nil || policyDuration == nil {
		return // Already has expiry or no duration
	}

	duration := time.Duration(*policyDuration) * time.Second
	expiry := time.Now().Add(duration)
	l.Expiry = &expiry
}

// SetExpiryOnFirstUse sets expiry on first usage increment (Ruby: set_expiry_on_first_use!)
func (l *License) SetExpiryOnFirstUse(policyDuration *int64) {
	if l.Expiry != nil || policyDuration == nil {
		return // Already has expiry or no duration
	}

	duration := time.Duration(*policyDuration) * time.Second
	expiry := time.Now().Add(duration)
	l.Expiry = &expiry
}

// ===== CHECK-IN METHODS - MAPPING FROM RUBY =====

// IsCheckInOverdue checks if license check-in is overdue (Ruby: check_in_overdue?)
func (l *License) IsCheckInOverdue(checkInInterval *int64, requiresCheckIn bool) bool {
	if !requiresCheckIn || checkInInterval == nil || l.LastCheckInAt == nil {
		return false
	}

	intervalDuration := time.Duration(*checkInInterval) * time.Second
	deadline := l.LastCheckInAt.Add(intervalDuration)
	return time.Now().After(deadline)
}

// GetNextCheckInAt returns next required check-in time (Ruby: next_check_in_at)
func (l *License) GetNextCheckInAt(checkInInterval *int64, requiresCheckIn bool) *time.Time {
	if !requiresCheckIn || checkInInterval == nil || l.LastCheckInAt == nil {
		return nil
	}

	intervalDuration := time.Duration(*checkInInterval) * time.Second
	nextCheckIn := l.LastCheckInAt.Add(intervalDuration)
	return &nextCheckIn
}

// ===== RENEWAL METHODS - MAPPING FROM RUBY =====

// Renew renews the license based on renewal strategy (Ruby: renew!)
func (l *License) Renew(policyDuration *int64, renewalStrategy string) error {
	if l.Expiry == nil || policyDuration == nil {
		return fmt.Errorf("cannot renew: license has no expiry or policy has no duration")
	}

	duration := time.Duration(*policyDuration) * time.Second
	now := time.Now()

	switch renewalStrategy {
	case "RENEW_FROM_EXPIRY":
		// Ruby: renew_from_expiry? - extend from current expiry
		newExpiry := l.Expiry.Add(duration)
		l.Expiry = &newExpiry
	case "RENEW_FROM_NOW":
		// Ruby: renew_from_now? - extend from now
		newExpiry := now.Add(duration)
		l.Expiry = &newExpiry
	case "RENEW_FROM_NOW_IF_EXPIRED":
		// Ruby: renew_from_now_if_expired? - extend from now if expired, otherwise from expiry
		var baseTime time.Time
		if l.IsExpired() {
			baseTime = now
		} else {
			baseTime = *l.Expiry
		}
		newExpiry := baseTime.Add(duration)
		l.Expiry = &newExpiry
	default:
		return fmt.Errorf("unknown renewal strategy: %s", renewalStrategy)
	}

	l.UpdatedAt = now
	return nil
}

// Transfer transfers license to new policy (Ruby: transfer!)
func (l *License) Transfer(newPolicyID string, resetExpiryOnTransfer bool, policyDuration *int64) {
	l.PolicyID = newPolicyID

	if resetExpiryOnTransfer {
		if policyDuration != nil {
			duration := time.Duration(*policyDuration) * time.Second
			expiry := time.Now().Add(duration)
			l.Expiry = &expiry
		} else {
			l.Expiry = nil // Remove expiry if no duration
		}
	}

	l.UpdatedAt = time.Now()
}

// ===== PROTECTED STATUS - MAPPING FROM RUBY =====

// IsProtected returns effective protected status (Ruby: protected?)
func (l *License) IsProtected(policyProtected bool) bool {
	// If license has explicit protected value, use it
	// Otherwise, inherit from policy (Ruby logic)
	return l.Protected || policyProtected
}

// ===== ACTIVATION METHODS - MAPPING FROM RUBY =====

// IsActivated checks if license has any machine activations (Ruby: activated scope)
func (l *License) IsActivated() bool {
	return l.MachinesCount >= 1
}

// IsAssigned checks if license has owner or users (Ruby: assigned scope)
func (l *License) IsAssigned() bool {
	return l.HasOwner() || l.LicenseUsersCount > 0
}

// IsUnassigned checks if license has no owner or users (Ruby: unassigned scope)
func (l *License) IsUnassigned() bool {
	return !l.IsAssigned()
}

// ===== KEY GENERATION METHODS - MAPPING FROM RUBY =====

// SetFirstCheckIn sets initial check-in timestamp (Ruby: set_first_check_in)
func (l *License) SetFirstCheckIn() {
	if l.LastCheckInAt != nil {
		return // Already has check-in time
	}

	now := time.Now()
	l.LastCheckInAt = &now
}

// GenerateDefaultKey generates a default unencrypted license key (Ruby: generate_unencrypted_key!)
// Uses crypto/rand for secure random generation and follows Ruby pattern
func (l *License) GenerateDefaultKey() string {
	// Generate UUID-based key for uniqueness (Go-style improvement)
	id := uuid.New()
	keyStr := strings.ReplaceAll(id.String(), "-", "")

	// Take first 16 characters and format like Ruby: XXXXXX-XXXXXX-XXXX
	if len(keyStr) >= 16 {
		keyStr = keyStr[:16]
	}

	// Format in groups: 6-6-4 characters (Ruby style)
	formatted := strings.ToUpper(keyStr[:6] + "-" + keyStr[6:12] + "-" + keyStr[12:16])

	return formatted
}

// === OVERAGE METHODS - MAPPING FROM RUBY ===
// Ruby delegates these methods to policy: delegate :always_allow_overage?, :allow_1_25x_overage?, etc.

// checkOverageStrategy is a helper method to check policy overage strategy (DRY principle)
func (l *License) checkOverageStrategy(policy *Policy, strategy OverageStrategy) bool {
	return policy != nil && policy.OverageStrategy != nil && *policy.OverageStrategy == strategy
}

// AlwaysAllowOverageWithPolicy checks if license always allows overage (Ruby: always_allow_overage?)
func (l *License) AlwaysAllowOverageWithPolicy(policy *Policy) bool {
	return l.checkOverageStrategy(policy, OverageAlwaysAllow)
}

// Allow125xOverageWithPolicy checks if license allows 1.25x overage (Ruby: allow_1_25x_overage?)
func (l *License) Allow125xOverageWithPolicy(policy *Policy) bool {
	return l.checkOverageStrategy(policy, OverageAllow125X)
}

// Allow15xOverageWithPolicy checks if license allows 1.5x overage (Ruby: allow_1_5x_overage?)
func (l *License) Allow15xOverageWithPolicy(policy *Policy) bool {
	return l.checkOverageStrategy(policy, OverageAllow15X)
}

// Allow2xOverageWithPolicy checks if license allows 2x overage (Ruby: allow_2x_overage?)
func (l *License) Allow2xOverageWithPolicy(policy *Policy) bool {
	return l.checkOverageStrategy(policy, OverageAllow2X)
}

// === OVERAGE METHODS WITHOUT POLICY PARAMETER (FOR BACKWARD COMPATIBILITY) ===
// These methods are used by machine validation and other places that don't have policy context
// They return conservative defaults (no overage allowed) since we can't load policy here

// AlwaysAllowOverage checks if license always allows overage (backward compatibility)
func (l *License) AlwaysAllowOverage() bool {
	// Conservative default: no overage allowed without policy context
	// This is used by machine validation where policy loading would be complex
	return false
}

// Allow125xOverage checks if license allows 1.25x overage (backward compatibility)
func (l *License) Allow125xOverage() bool {
	return false
}

// Allow15xOverage checks if license allows 1.5x overage (backward compatibility)
func (l *License) Allow15xOverage() bool {
	return false
}

// Allow2xOverage checks if license allows 2x overage (backward compatibility)
func (l *License) Allow2xOverage() bool {
	return false
}

// ===== VALIDATION HELPERS - MAPPING FROM RUBY =====

// HasExpiry checks if license has expiry date (Ruby: expires?)
func (l *License) HasExpiry() bool {
	return l.Expiry != nil
}

// HasUserID checks if license has a user_id (Ruby: user_id.nil? check)
// Pure entity logic - no database access
func (l *License) HasUserID() bool {
	return l.UserID != nil && *l.UserID != ""
}

// NOTE: IsBanned() logic moved to LicenseService.isLicenseBanned()
// Ruby logic: return false if user_id.nil? || owner.nil?; owner.banned?
// This requires User repository access to:
// 1. Check if user_id exists (HasUserID() - entity level)
// 2. Load owner from database (service level)
// 3. Check if owner exists (service level)
// 4. Check owner.banned? status (service level)

// checkExpirationStrategy is a helper method to check policy expiration strategy (DRY principle)
func (l *License) checkExpirationStrategy(policy *Policy, strategy ExpirationStrategy) bool {
	return policy != nil && policy.ExpirationStrategy != nil && *policy.ExpirationStrategy == strategy
}

// RevokeAccessWithPolicy checks if access should be revoked on expiry (Ruby: revoke_access?)
// Ruby delegates to policy: delegate :revoke_access?
func (l *License) RevokeAccessWithPolicy(policy *Policy) bool {
	if policy == nil || policy.ExpirationStrategy == nil {
		return true // Default: revoke access on expiry
	}
	return l.checkExpirationStrategy(policy, ExpirationRevokeAccess)
}

// AllowAccessWithPolicy checks if access is allowed even when expired (Ruby: allow_access?)
// Ruby delegates to policy: delegate :allow_access?
func (l *License) AllowAccessWithPolicy(policy *Policy) bool {
	return l.checkExpirationStrategy(policy, ExpirationAllowAccess)
}

// MaintainAccessWithPolicy checks if access is maintained even when expired (Ruby: maintain_access?)
// Ruby delegates to policy: delegate :maintain_access?
func (l *License) MaintainAccessWithPolicy(policy *Policy) bool {
	return l.checkExpirationStrategy(policy, ExpirationMaintainAccess)
}

// === ACCESS METHODS WITHOUT POLICY PARAMETER (FOR BACKWARD COMPATIBILITY) ===

// RevokeAccess checks if access should be revoked on expiry (backward compatibility)
func (l *License) RevokeAccess() bool {
	return true // Conservative default: revoke access on expiry
}

// AllowAccess checks if access is allowed even when expired (backward compatibility)
func (l *License) AllowAccess() bool {
	return false // Conservative default: no access when expired
}

// MaintainAccess checks if access is maintained even when expired (backward compatibility)
func (l *License) MaintainAccess() bool {
	return false // Conservative default: no access maintenance when expired
}

// CanBeRenewed checks if license can be renewed (Ruby: renew! validation)
func (l *License) CanBeRenewed(policyDuration *int64) bool {
	return l.Expiry != nil && policyDuration != nil
}

// ===== COMPUTED FIELDS - MAPPING FROM RUBY =====

// GetOwnerID returns owner ID for API compatibility (Ruby: owner_id method)
func (l *License) GetOwnerID() *string {
	return l.UserID
}

// SetOwnerID sets owner ID for API compatibility (Ruby: owner_id= method)
func (l *License) SetOwnerID(ownerID *string) {
	l.UserID = ownerID
}

// ===== RESOURCE TRACKING - MAPPING FROM RUBY =====

// CanIncrementUsage checks if usage can be incremented without exceeding limits
func (l *License) CanIncrementUsage(maxUses *int) bool {
	return l.hasRemainingResource(l.Uses, maxUses)
}

// GetRemainingUses returns remaining usage count
func (l *License) GetRemainingUses(maxUses *int) *int {
	return l.getRemainingResource(l.Uses, maxUses)
}

// hasRemainingResource is a helper method to check if resource has remaining capacity (DRY principle)
func (l *License) hasRemainingResource(current int, max *int) bool {
	return max == nil || current < *max
}

// getRemainingResource is a helper method to calculate remaining resource count (DRY principle)
func (l *License) getRemainingResource(current int, max *int) *int {
	if max == nil {
		return nil // Unlimited
	}
	remaining := *max - current
	if remaining < 0 {
		remaining = 0
	}
	return &remaining
}

// CanActivateMachine checks if a new machine can be activated
func (l *License) CanActivateMachine(maxMachines *int) bool {
	return l.hasRemainingResource(l.MachinesCount, maxMachines)
}

// GetRemainingMachines returns remaining machine activation count
func (l *License) GetRemainingMachines(maxMachines *int) *int {
	return l.getRemainingResource(l.MachinesCount, maxMachines)
}

// CanAllocateCores checks if cores can be allocated
func (l *License) CanAllocateCores(requestedCores int, maxCores *int) bool {
	if maxCores == nil {
		return true // No limit
	}
	return (l.MachinesCoreCount + requestedCores) <= *maxCores
}

// GetRemainingCores returns remaining core allocation count
func (l *License) GetRemainingCores(maxCores *int) *int {
	return l.getRemainingResource(l.MachinesCoreCount, maxCores)
}
