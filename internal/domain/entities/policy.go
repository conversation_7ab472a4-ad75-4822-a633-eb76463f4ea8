package entities

import (
	"errors"
	"time"

	"gorm.io/gorm"
)

// ===== POLICY ENTITY - GOLANG STYLE =====
// Sử dụng custom types từ policy_strategies.go để type safety và validation mạnh mẽ

var (
	ErrUnsupportedPool = errors.New("policy does not support pool")
	ErrEmptyPool       = errors.New("policy pool is empty")
)

type Policy struct {
	ID             string `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	OrganizationID string `json:"organization_id" gorm:"type:uuid;not null"`
	ProductID      string `json:"product_id" gorm:"type:uuid;not null"`
	Name           string `json:"name" gorm:"size:255;not null"`

	// Basic configuration
	Strict      bool  `json:"strict" gorm:"default:false"`
	Protected   *bool `json:"protected"`
	Duration    *int  `json:"duration"` // in seconds
	LockVersion int   `json:"lock_version" gorm:"default:0"`

	// License behavior
	Floating  bool `json:"floating" gorm:"default:false"`  // License transferability
	UsePool   bool `json:"use_pool" gorm:"default:false"`  // License pooling
	Encrypted bool `json:"encrypted" gorm:"default:false"` // License encryption

	// Cryptographic scheme - Sử dụng custom type để type safety
	Scheme *CryptoScheme `json:"scheme" gorm:"type:text"`

	// Limits and constraints
	MaxMachines  *int `json:"max_machines,omitempty"`
	MaxUses      *int `json:"max_uses,omitempty"`
	MaxCores     *int `json:"max_cores,omitempty"`
	MaxUsers     *int `json:"max_users,omitempty"`
	MaxProcesses *int `json:"max_processes,omitempty"`

	// Heartbeat configuration - Sử dụng custom types
	RequireHeartbeat              bool                           `json:"require_heartbeat" gorm:"default:false"`
	HeartbeatDuration             *int                           `json:"heartbeat_duration"` // in seconds
	HeartbeatBasis                *HeartbeatBasis                `json:"heartbeat_basis" gorm:"type:text"`
	HeartbeatCullStrategy         *HeartbeatCullStrategy         `json:"heartbeat_cull_strategy" gorm:"type:text"`
	HeartbeatResurrectionStrategy *HeartbeatResurrectionStrategy `json:"heartbeat_resurrection_strategy" gorm:"type:text"`

	// Check-in configuration
	RequireCheckIn       bool    `json:"require_check_in" gorm:"default:false"`
	CheckInInterval      *string `json:"check_in_interval"`
	CheckInIntervalCount *int    `json:"check_in_interval_count"`

	// Machine strategies - Sử dụng custom types để validation mạnh mẽ
	MachineUniquenessStrategy *MachineUniquenessStrategy `json:"machine_uniqueness_strategy" gorm:"type:text"`
	MachineMatchingStrategy   *MatchingStrategy          `json:"machine_matching_strategy" gorm:"type:text"`
	MachineLeasingStrategy    *LeasingStrategy           `json:"machine_leasing_strategy" gorm:"type:text"`

	// Component strategies - Sử dụng custom types
	ComponentUniquenessStrategy *ComponentUniquenessStrategy `json:"component_uniqueness_strategy" gorm:"type:text"`
	ComponentMatchingStrategy   *MatchingStrategy            `json:"component_matching_strategy" gorm:"type:text"`

	// Process strategies - Sử dụng custom types
	ProcessLeasingStrategy *LeasingStrategy `json:"process_leasing_strategy" gorm:"type:text"`

	// Expiration configuration - Sử dụng custom types
	ExpirationStrategy *ExpirationStrategy `json:"expiration_strategy" gorm:"type:text"`
	ExpirationBasis    *ExpirationBasis    `json:"expiration_basis" gorm:"type:text"`
	RenewalBasis       *RenewalBasis       `json:"renewal_basis" gorm:"type:text"`

	// Authentication and transfer - Sử dụng custom types
	AuthenticationStrategy *AuthenticationStrategy `json:"authentication_strategy" gorm:"type:text"`
	TransferStrategy       *TransferStrategy       `json:"transfer_strategy" gorm:"type:text"`
	OverageStrategy        *OverageStrategy        `json:"overage_strategy" gorm:"type:text"`

	// Scope requirements
	RequireProductScope     bool `json:"require_product_scope" gorm:"default:false"`
	RequirePolicyScope      bool `json:"require_policy_scope" gorm:"default:false"`
	RequireMachineScope     bool `json:"require_machine_scope" gorm:"default:false"`
	RequireFingerprintScope bool `json:"require_fingerprint_scope" gorm:"default:false"`
	RequireUserScope        bool `json:"require_user_scope" gorm:"default:false"`
	RequireChecksumScope    bool `json:"require_checksum_scope" gorm:"default:false"`
	RequireVersionScope     bool `json:"require_version_scope" gorm:"default:false"`
	RequireComponentsScope  bool `json:"require_components_scope" gorm:"default:false"`

	Metadata Metadata `json:"metadata" gorm:"type:jsonb;default:'{}'"`

	// Timestamps
	CreatedAt time.Time      `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"autoUpdateTime"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// Relationships
	Organization *Organization `json:"organization,omitempty" gorm:"foreignKey:OrganizationID"`
	Product      *Product      `json:"product,omitempty" gorm:"foreignKey:ProductID"`
	Licenses     []License     `json:"licenses,omitempty" gorm:"foreignKey:PolicyID"`
}

// TableName returns the table name for Policy
func (Policy) TableName() string {
	return "policies"
}

// BeforeCreate hook để set default strategies
func (p *Policy) BeforeCreate(tx *gorm.DB) error {
	p.SetDefaultStrategies()
	return nil
}

// BeforeUpdate hook để validate strategies
func (p *Policy) BeforeUpdate(tx *gorm.DB) error {
	return p.ValidateStrategies()
}

// ===== BASIC POLICY METHODS =====

// IsStrict kiểm tra policy có strict mode không
func (p *Policy) IsStrict() bool {
	return p.Strict
}

// IsNodeLocked kiểm tra policy có node-locked không (Ruby: node_locked?)
func (p *Policy) IsNodeLocked() bool {
	// Node-locked means exactly 1 machine allowed
	return p.MaxMachines != nil && *p.MaxMachines == 1
}

// IsProtected kiểm tra policy có được bảo vệ không
func (p *Policy) IsProtected() bool {
	return p.Protected != nil && *p.Protected
}

// IsFloating kiểm tra license có thể transfer không
func (p *Policy) IsFloating() bool {
	return p.Floating
}

// IsEncrypted kiểm tra license có được mã hóa không
func (p *Policy) IsEncrypted() bool {
	return p.Encrypted
}

// UsesPool kiểm tra policy có sử dụng key pool không
func (p *Policy) UsesPool() bool {
	return p.UsePool
}

// HasScheme kiểm tra policy có sử dụng crypto scheme không
func (p *Policy) HasScheme() bool {
	return p.Scheme != nil && p.Scheme.IsValid()
}

// RequiresHeartbeat kiểm tra có yêu cầu heartbeat không
func (p *Policy) RequiresHeartbeat() bool {
	return p.RequireHeartbeat
}

// RequiresCheckIn kiểm tra có yêu cầu check-in không
func (p *Policy) RequiresCheckIn() bool {
	return p.RequireCheckIn
}

// HasDuration kiểm tra policy có giới hạn thời gian không
func (p *Policy) HasDuration() bool {
	return p.Duration != nil && *p.Duration > 0
}

// GetDuration trả về duration trong seconds
func (p *Policy) GetDuration() int {
	if p.Duration == nil {
		return 0
	}
	return *p.Duration
}

// ===== OVERAGE METHODS =====
// Go implementation uses overage_strategy directly - no legacy 'concurrent' field needed

// AllowsOverage kiểm tra có cho phép vượt quá giới hạn không
func (p *Policy) AllowsOverage() bool {
	if p.OverageStrategy == nil {
		return false
	}
	return p.OverageStrategy.AllowsOverage()
}

// GetOverageMultiplier trả về hệ số overage
func (p *Policy) GetOverageMultiplier() float64 {
	if p.OverageStrategy == nil {
		return 1.0
	}
	return p.OverageStrategy.MaxOverageMultiplier()
}

// ===== STRATEGY HELPER METHODS =====

// GetMachineUniquenessRank trả về rank của machine uniqueness strategy
func (p *Policy) GetMachineUniquenessRank() int {
	if p.MachineUniquenessStrategy == nil {
		return MachineUniquePerLicense.Rank()
	}
	return p.MachineUniquenessStrategy.Rank()
}

// GetComponentUniquenessRank trả về rank của component uniqueness strategy
func (p *Policy) GetComponentUniquenessRank() int {
	if p.ComponentUniquenessStrategy == nil {
		return ComponentUniquePerMachine.Rank()
	}
	return p.ComponentUniquenessStrategy.Rank()
}

// GetRequiredMachineMatches tính số machine matches cần thiết
func (p *Policy) GetRequiredMachineMatches(totalComponents int) int {
	if p.MachineMatchingStrategy == nil {
		return MatchAny.RequiredMatches(totalComponents)
	}
	return p.MachineMatchingStrategy.RequiredMatches(totalComponents)
}

// GetRequiredComponentMatches tính số component matches cần thiết
func (p *Policy) GetRequiredComponentMatches(totalComponents int) int {
	if p.ComponentMatchingStrategy == nil {
		return MatchAny.RequiredMatches(totalComponents)
	}
	return p.ComponentMatchingStrategy.RequiredMatches(totalComponents)
}

// ===== AUTHENTICATION METHODS =====

// SupportsTokenAuth kiểm tra có hỗ trợ token authentication không
func (p *Policy) SupportsTokenAuth() bool {
	if p.AuthenticationStrategy == nil {
		return true // Default
	}
	return p.AuthenticationStrategy.SupportsToken()
}

// SupportsLicenseAuth kiểm tra có hỗ trợ license authentication không
func (p *Policy) SupportsLicenseAuth() bool {
	if p.AuthenticationStrategy == nil {
		return false
	}
	return p.AuthenticationStrategy.SupportsLicense()
}

// RequiresAuth kiểm tra có yêu cầu authentication không
func (p *Policy) RequiresAuth() bool {
	if p.AuthenticationStrategy == nil {
		return true // Default
	}
	return p.AuthenticationStrategy.RequiresAuth()
}

// ===== EXPIRATION METHODS =====

// AllowsAccessWhenExpired kiểm tra có cho phép truy cập khi expired không
func (p *Policy) AllowsAccessWhenExpired() bool {
	if p.ExpirationStrategy == nil {
		return false // Default restrict access
	}
	return p.ExpirationStrategy.AllowsAccess()
}

// ResetsExpiryOnTransfer kiểm tra có reset expiry khi transfer không
func (p *Policy) ResetsExpiryOnTransfer() bool {
	if p.TransferStrategy == nil {
		return false // Default keep expiry
	}
	return p.TransferStrategy.ResetsExpiry()
}

// AlwaysResurrectDead kiểm tra có luôn resurrect machine chết không
func (p *Policy) AlwaysResurrectDead() bool {
	if p.HeartbeatResurrectionStrategy == nil {
		return false
	}
	return *p.HeartbeatResurrectionStrategy == HeartbeatAlwaysRevive
}

// GetLazarusTTL trả về thời gian TTL cho lazarus resurrection (seconds)
func (p *Policy) GetLazarusTTL() int {
	if p.HeartbeatResurrectionStrategy == nil {
		return 0
	}
	return p.HeartbeatResurrectionStrategy.GetTTLSeconds()
}

// MachineUniquePerAccount kiểm tra machine unique per account
func (p *Policy) MachineUniquePerAccount() bool {
	if p.MachineUniquenessStrategy == nil {
		return false
	}
	return *p.MachineUniquenessStrategy == MachineUniquePerAccount
}

// MachineUniquePerProduct kiểm tra machine unique per product
func (p *Policy) MachineUniquePerProduct() bool {
	if p.MachineUniquenessStrategy == nil {
		return false
	}
	return *p.MachineUniquenessStrategy == MachineUniquePerProduct
}

// MachineUniquePerPolicy kiểm tra machine unique per policy
func (p *Policy) MachineUniquePerPolicy() bool {
	if p.MachineUniquenessStrategy == nil {
		return false
	}
	return *p.MachineUniquenessStrategy == MachineUniquePerPolicy
}

// MachineUniquePerLicense kiểm tra machine unique per license
func (p *Policy) MachineUniquePerLicense() bool {
	if p.MachineUniquenessStrategy == nil {
		return true // Default
	}
	return *p.MachineUniquenessStrategy == MachineUniquePerLicense
}

// MachineLeasePerLicense kiểm tra machine lease per license
func (p *Policy) MachineLeasePerLicense() bool {
	if p.MachineLeasingStrategy == nil {
		return true // Default
	}
	return *p.MachineLeasingStrategy == LeasingPerLicense
}

// MachineLeasePerUser kiểm tra machine lease per user
func (p *Policy) MachineLeasePerUser() bool {
	if p.MachineLeasingStrategy == nil {
		return false
	}
	return *p.MachineLeasingStrategy == LeasingPerUser
}

// ComponentMatchAny kiểm tra component matching strategy là ANY
func (p *Policy) ComponentMatchAny() bool {
	if p.ComponentMatchingStrategy == nil {
		return true // Default
	}
	return *p.ComponentMatchingStrategy == MatchAny
}

// ComponentMatchTwo kiểm tra component matching strategy là TWO
func (p *Policy) ComponentMatchTwo() bool {
	if p.ComponentMatchingStrategy == nil {
		return false
	}
	return *p.ComponentMatchingStrategy == MatchTwo
}

// ComponentMatchMost kiểm tra component matching strategy là MOST
func (p *Policy) ComponentMatchMost() bool {
	if p.ComponentMatchingStrategy == nil {
		return false
	}
	return *p.ComponentMatchingStrategy == MatchMost
}

// ComponentMatchAll kiểm tra component matching strategy là ALL
func (p *Policy) ComponentMatchAll() bool {
	if p.ComponentMatchingStrategy == nil {
		return false
	}
	return *p.ComponentMatchingStrategy == MatchAll
}

// ===== HEARTBEAT METHODS =====

// DeactivatesDeadMachines kiểm tra có deactivate machine chết không
func (p *Policy) DeactivatesDeadMachines() bool {
	if p.HeartbeatCullStrategy == nil {
		return true // Default
	}
	return p.HeartbeatCullStrategy.DeactivatesDead()
}

// ResurrectsDeadMachines kiểm tra có hồi sinh machine chết không
func (p *Policy) ResurrectsDeadMachines() bool {
	if p.HeartbeatResurrectionStrategy == nil {
		return false // Default
	}
	return p.HeartbeatResurrectionStrategy.ResurrectsDead()
}
