package entities

import (
	"fmt"
)

// ===== POLICY VALIDATION METHODS - GOLANG STYLE =====
// Sử dụng sức mạnh của Go để validation mạnh mẽ và type-safe

// ValidateStrategies kiểm tra tất cả policy strategies có hợp lệ không
//
// Thực hiện validation cơ bản cho tất cả strategy fields:
// - Kiểm tra enum values có trong danh sách allowed values không
// - Không kiểm tra business logic compatibility (dùng ValidateBusinessRules cho việc đó)
// - Trả về error đầu tiên gặp phải (fail-fast approach)
//
// Use case: Validate request data trước khi lưu vào database
// Return: nil nếu tất cả strategies hợp lệ, error nếu có strategy không hợp lệ
func (p *Policy) ValidateStrategies() error {
	// Validate crypto scheme
	if p.Scheme != nil && !p.Scheme.IsValid() {
		return fmt.Errorf("invalid crypto scheme: %s", *p.Scheme)
	}

	// Validate machine uniqueness strategy
	if p.MachineUniquenessStrategy != nil && !p.MachineUniquenessStrategy.IsValid() {
		return fmt.Errorf("invalid machine uniqueness strategy: %s", *p.MachineUniquenessStrategy)
	}

	// Validate machine matching strategy
	if p.MachineMatchingStrategy != nil && !p.MachineMatchingStrategy.IsValid() {
		return fmt.Errorf("invalid machine matching strategy: %s", *p.MachineMatchingStrategy)
	}

	// Validate component uniqueness strategy
	if p.ComponentUniquenessStrategy != nil && !p.ComponentUniquenessStrategy.IsValid() {
		return fmt.Errorf("invalid component uniqueness strategy: %s", *p.ComponentUniquenessStrategy)
	}

	// Validate component matching strategy
	if p.ComponentMatchingStrategy != nil && !p.ComponentMatchingStrategy.IsValid() {
		return fmt.Errorf("invalid component matching strategy: %s", *p.ComponentMatchingStrategy)
	}

	// Validate expiration strategy
	if p.ExpirationStrategy != nil && !p.ExpirationStrategy.IsValid() {
		return fmt.Errorf("invalid expiration strategy: %s", *p.ExpirationStrategy)
	}

	// Validate expiration basis
	if p.ExpirationBasis != nil && !p.ExpirationBasis.IsValid() {
		return fmt.Errorf("invalid expiration basis: %s", *p.ExpirationBasis)
	}

	// Validate renewal basis
	if p.RenewalBasis != nil && !p.RenewalBasis.IsValid() {
		return fmt.Errorf("invalid renewal basis: %s", *p.RenewalBasis)
	}

	// Validate transfer strategy
	if p.TransferStrategy != nil && !p.TransferStrategy.IsValid() {
		return fmt.Errorf("invalid transfer strategy: %s", *p.TransferStrategy)
	}

	// Validate authentication strategy
	if p.AuthenticationStrategy != nil && !p.AuthenticationStrategy.IsValid() {
		return fmt.Errorf("invalid authentication strategy: %s", *p.AuthenticationStrategy)
	}

	// Validate overage strategy
	if p.OverageStrategy != nil && !p.OverageStrategy.IsValid() {
		return fmt.Errorf("invalid overage strategy: %s", *p.OverageStrategy)
	}

	// Validate heartbeat strategies
	if p.HeartbeatCullStrategy != nil && !p.HeartbeatCullStrategy.IsValid() {
		return fmt.Errorf("invalid heartbeat cull strategy: %s", *p.HeartbeatCullStrategy)
	}

	if p.HeartbeatResurrectionStrategy != nil && !p.HeartbeatResurrectionStrategy.IsValid() {
		return fmt.Errorf("invalid heartbeat resurrection strategy: %s", *p.HeartbeatResurrectionStrategy)
	}

	if p.HeartbeatBasis != nil && !p.HeartbeatBasis.IsValid() {
		return fmt.Errorf("invalid heartbeat basis: %s", *p.HeartbeatBasis)
	}

	// Validate leasing strategies
	if p.MachineLeasingStrategy != nil && !p.MachineLeasingStrategy.IsValidForMachine() {
		return fmt.Errorf("invalid machine leasing strategy: %s", *p.MachineLeasingStrategy)
	}

	if p.ProcessLeasingStrategy != nil && !p.ProcessLeasingStrategy.IsValidForProcess() {
		return fmt.Errorf("invalid process leasing strategy: %s", *p.ProcessLeasingStrategy)
	}

	return nil
}

// ValidateOverageWithLimits kiểm tra overage strategy có tương thích với limits không
func (p *Policy) ValidateOverageWithLimits() error {
	if p.OverageStrategy == nil {
		return nil
	}

	isNodeLocked := !p.Floating

	// Validate với max_machines
	if p.MaxMachines != nil {
		if err := p.OverageStrategy.ValidateWithLimit(*p.MaxMachines, isNodeLocked); err != nil {
			return fmt.Errorf("max_machines validation failed: %w", err)
		}
	}

	// Validate với max_cores
	if p.MaxCores != nil {
		if err := p.OverageStrategy.ValidateWithLimit(*p.MaxCores, isNodeLocked); err != nil {
			return fmt.Errorf("max_cores validation failed: %w", err)
		}
	}

	// Validate với max_processes
	if p.MaxProcesses != nil {
		if err := p.OverageStrategy.ValidateWithLimit(*p.MaxProcesses, isNodeLocked); err != nil {
			return fmt.Errorf("max_processes validation failed: %w", err)
		}
	}

	// Validate với max_users
	if p.MaxUsers != nil {
		if err := p.OverageStrategy.ValidateWithLimit(*p.MaxUsers, isNodeLocked); err != nil {
			return fmt.Errorf("max_users validation failed: %w", err)
		}
	}

	return nil
}

// ValidateHeartbeatCompatibility kiểm tra heartbeat strategies có tương thích không
func (p *Policy) ValidateHeartbeatCompatibility() error {
	// Nếu resurrection strategy là ALWAYS_REVIVE thì cull strategy phải là KEEP_DEAD
	if p.HeartbeatResurrectionStrategy != nil &&
		*p.HeartbeatResurrectionStrategy == HeartbeatAlwaysRevive {
		if p.HeartbeatCullStrategy == nil ||
			*p.HeartbeatCullStrategy != HeartbeatKeepDead {
			return fmt.Errorf("heartbeat cull strategy must be KEEP_DEAD when resurrection strategy is ALWAYS_REVIVE")
		}
	}

	return nil
}

// SetDefaultStrategies thiết lập default values cho strategies (tương đương Ruby before_create callback)
//
// Tự động set default values cho các strategy fields khi chúng là nil.
// Go implementation sử dụng modern defaults (không có legacy compatibility).
//
// Default strategy philosophy:
// - Ưu tiên security và compliance (không quá lỏng lẻo)
// - Balance giữa user experience và license protection
// - Sử dụng modern crypto schemes và techniques
//
// Được gọi tự động trong:
// - CreatePolicy handler (trước khi validate và save)
// - GORM BeforeCreate hook (backup safety net)
//
// Note: Chỉ set default cho nil fields, không override existing values
func (p *Policy) SetDefaultStrategies() {
	// Machine strategies defaults
	if p.MachineUniquenessStrategy == nil {
		strategy := MachineUniquePerLicense
		p.MachineUniquenessStrategy = &strategy
	}

	if p.MachineMatchingStrategy == nil {
		strategy := MatchAny
		p.MachineMatchingStrategy = &strategy
	}

	if p.MachineLeasingStrategy == nil {
		strategy := LeasingPerLicense
		p.MachineLeasingStrategy = &strategy
	}

	// Component strategies defaults
	if p.ComponentUniquenessStrategy == nil {
		strategy := ComponentUniquePerMachine
		p.ComponentUniquenessStrategy = &strategy
	}

	if p.ComponentMatchingStrategy == nil {
		strategy := MatchAny
		p.ComponentMatchingStrategy = &strategy
	}

	// Process strategy default
	if p.ProcessLeasingStrategy == nil {
		strategy := LeasingPerMachine
		p.ProcessLeasingStrategy = &strategy
	}

	// Expiration strategies defaults
	if p.ExpirationStrategy == nil {
		strategy := ExpirationRestrictAccess
		p.ExpirationStrategy = &strategy
	}

	if p.ExpirationBasis == nil {
		basis := ExpirationFromCreation
		p.ExpirationBasis = &basis
	}

	if p.RenewalBasis == nil {
		basis := RenewalFromExpiry
		p.RenewalBasis = &basis
	}

	// Transfer strategy default
	if p.TransferStrategy == nil {
		strategy := TransferKeepExpiry
		p.TransferStrategy = &strategy
	}

	// Authentication strategy default
	if p.AuthenticationStrategy == nil {
		strategy := AuthToken
		p.AuthenticationStrategy = &strategy
	}

	// Heartbeat strategies defaults
	if p.HeartbeatCullStrategy == nil {
		strategy := HeartbeatDeactivateDead
		p.HeartbeatCullStrategy = &strategy
	}

	if p.HeartbeatResurrectionStrategy == nil {
		strategy := HeartbeatNoRevive
		p.HeartbeatResurrectionStrategy = &strategy
	}

	if p.HeartbeatBasis == nil {
		basis := HeartbeatFromCreation
		p.HeartbeatBasis = &basis
	}

	// Overage strategy default (Go implementation uses latest defaults)
	if p.OverageStrategy == nil {
		strategy := OverageNoOverage // Modern default - no overage
		p.OverageStrategy = &strategy
	}

	// Crypto scheme default (Go implementation uses latest ED25519)
	if p.Scheme == nil {
		scheme := CryptoSchemeED25519 // Latest, fastest and most secure default
		p.Scheme = &scheme
	}
}

// GetEffectiveOverageMultiplier trả về hệ số overage hiệu quả
func (p *Policy) GetEffectiveOverageMultiplier() float64 {
	if p.OverageStrategy == nil {
		return 1.0
	}
	return p.OverageStrategy.MaxOverageMultiplier()
}

// CalculateEffectiveLimit tính toán limit hiệu quả với overage
func (p *Policy) CalculateEffectiveLimit(baseLimit int) int {
	multiplier := p.GetEffectiveOverageMultiplier()
	if multiplier < 0 {
		return -1 // Unlimited
	}
	return int(float64(baseLimit) * multiplier)
}

// RequiresAuthentication kiểm tra có yêu cầu authentication không
func (p *Policy) RequiresAuthentication() bool {
	if p.AuthenticationStrategy == nil {
		return true // Default
	}
	return p.AuthenticationStrategy.RequiresAuth()
}

// ValidateBusinessRules kiểm tra các business rules như Ruby validations
func (p *Policy) ValidateBusinessRules() error {
	// Duration validation (Ruby: validates :duration)
	if p.Duration != nil {
		if *p.Duration <= 0 || *p.Duration > 2147483647 {
			return fmt.Errorf("duration must be between 1 and 2147483647")
		}
		if *p.Duration < 86400 { // 1 day
			return fmt.Errorf("duration must be greater than or equal to 86400 (1 day)")
		}
	}

	// Heartbeat duration validation (Ruby: validates :heartbeat_duration)
	if p.HeartbeatDuration != nil {
		if *p.HeartbeatDuration <= 0 || *p.HeartbeatDuration > 2147483647 {
			return fmt.Errorf("heartbeat_duration must be between 1 and 2147483647")
		}
		if *p.HeartbeatDuration < 60 { // 1 minute
			return fmt.Errorf("heartbeat_duration must be greater than or equal to 60 (1 minute)")
		}
	}

	// Max machines validation (Ruby: validates :max_machines)
	if p.MaxMachines != nil {
		if *p.MaxMachines < 0 || *p.MaxMachines > 2147483647 {
			return fmt.Errorf("max_machines must be between 0 and 2147483647")
		}
		if p.Floating && *p.MaxMachines < 1 {
			return fmt.Errorf("max_machines must be greater than or equal to 1 for floating policy")
		}
		if !p.Floating && *p.MaxMachines != 1 {
			return fmt.Errorf("max_machines must be equal to 1 for non-floating policy")
		}
	}

	// Max cores validation (Ruby: validates :max_cores)
	if p.MaxCores != nil {
		if *p.MaxCores < 1 || *p.MaxCores > 2147483647 {
			return fmt.Errorf("max_cores must be between 1 and 2147483647")
		}
	}

	// Max uses validation (Ruby: validates :max_uses)
	if p.MaxUses != nil {
		if *p.MaxUses < 0 || *p.MaxUses > 2147483647 {
			return fmt.Errorf("max_uses must be between 0 and 2147483647")
		}
	}

	// Max processes validation (Ruby: validates :max_processes)
	if p.MaxProcesses != nil {
		if *p.MaxProcesses <= 0 || *p.MaxProcesses > 2147483647 {
			return fmt.Errorf("max_processes must be between 1 and 2147483647")
		}
	}

	// Max users validation (Ruby: validates :max_users)
	if p.MaxUsers != nil {
		if *p.MaxUsers <= 0 || *p.MaxUsers > 2147483647 {
			return fmt.Errorf("max_users must be between 1 and 2147483647")
		}
	}

	// Check-in validation (Ruby: validates :check_in_interval, :check_in_interval_count)
	if p.RequireCheckIn {
		if p.CheckInInterval == nil {
			return fmt.Errorf("check_in_interval is required when require_check_in is true")
		}
		validIntervals := []string{"day", "week", "month", "year"}
		isValidInterval := false
		for _, valid := range validIntervals {
			if *p.CheckInInterval == valid {
				isValidInterval = true
				break
			}
		}
		if !isValidInterval {
			return fmt.Errorf("check_in_interval must be one of: day, week, month, year")
		}

		if p.CheckInIntervalCount == nil {
			return fmt.Errorf("check_in_interval_count is required when require_check_in is true")
		}
		if *p.CheckInIntervalCount < 1 || *p.CheckInIntervalCount > 365 {
			return fmt.Errorf("check_in_interval_count must be a number between 1 and 365 inclusive")
		}
	}

	// Metadata validation (Ruby: validates :metadata, length: { maximum: 64 })
	if len(p.Metadata) > 64 {
		return fmt.Errorf("metadata too many keys (exceeded limit of 64 keys)")
	}

	return nil
}

// ValidateSchemeCompatibility kiểm tra scheme compatibility
func (p *Policy) ValidateSchemeCompatibility() error {
	// Pool compatibility checks
	if p.UsePool {
		if p.Encrypted {
			return fmt.Errorf("cannot be encrypted and use a pool")
		}
		if p.Scheme != nil {
			return fmt.Errorf("cannot use a scheme and use a pool")
		}
	}

	// Go implementation only supports modern crypto schemes
	// No legacy compatibility needed - clean, secure implementation

	return nil
}
