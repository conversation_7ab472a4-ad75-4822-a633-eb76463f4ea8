package license

import (
	"context"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
)

// CacheInterface defines the caching interface for license services
type CacheInterface interface {
	Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error
	Get(ctx context.Context, key string) (string, error)
	Delete(ctx context.Context, key string) error
	Exists(ctx context.Context, key string) (bool, error)
}

// === LICENSE VALIDATION SERVICE - MAPPING FROM RUBY ===
// Service xác thực license mapping từ Ruby LicenseValidationService
// Tận dụng sức mạnh của Go: type safety, error handling, context, channels

// ValidationService handles license validation business logic
type ValidationService struct {
	licenseRepo repositories.LicenseRepository
	machineRepo repositories.MachineRepository
	userRepo    repositories.UserRepository
	policyRepo  repositories.PolicyRepository
}

// NewValidationService creates new validation service
func NewValidationService(
	licenseRepo repositories.LicenseRepository,
	machineRepo repositories.MachineRepository,
	userRepo repositories.UserRepository,
	policyRepo repositories.PolicyRepository,
) *ValidationService {
	return &ValidationService{
		licenseRepo: licenseRepo,
		machineRepo: machineRepo,
		userRepo:    userRepo,
		policyRepo:  policyRepo,
	}
}

// === VALIDATION RESULT TYPES - GO STYLE ===

// ValidationCode represents validation result codes (Ruby: validation codes)
type ValidationCode string

const (
	ValidationCodeValid               ValidationCode = "VALID"
	ValidationCodeNotFound            ValidationCode = "NOT_FOUND"
	ValidationCodeExpired             ValidationCode = "EXPIRED"
	ValidationCodeSuspended           ValidationCode = "SUSPENDED"
	ValidationCodeBanned              ValidationCode = "BANNED"
	ValidationCodeOverdue             ValidationCode = "OVERDUE"
	ValidationCodeNoMachine           ValidationCode = "NO_MACHINE"
	ValidationCodeTooManyMachines     ValidationCode = "TOO_MANY_MACHINES"
	ValidationCodeTooManyProcesses    ValidationCode = "TOO_MANY_PROCESSES"
	ValidationCodeTooManyCores        ValidationCode = "TOO_MANY_CORES"
	ValidationCodeFingerprintMismatch ValidationCode = "FINGERPRINT_MISMATCH"
	ValidationCodeInvalidScope        ValidationCode = "INVALID_SCOPE"
)

// ValidationResult contains license validation result (Ruby: validation result)
type ValidationResult struct {
	Valid     bool              `json:"valid"`
	Code      ValidationCode    `json:"code"`
	Detail    string            `json:"detail"`
	License   *entities.License `json:"license,omitempty"`
	Machine   *entities.Machine `json:"machine,omitempty"`
	Timestamp time.Time         `json:"timestamp"`
	Scope     *ValidationScope  `json:"scope,omitempty"`
	Nonce     *string           `json:"nonce,omitempty"`
}

// ValidationScope defines validation scope (Ruby: validation scope)
type ValidationScope struct {
	Fingerprint *string        `json:"fingerprint,omitempty"`
	UserID      *string        `json:"user_id,omitempty"`
	ProductID   *string        `json:"product_id,omitempty"`
	Metadata    map[string]any `json:"metadata,omitempty"`
}

// ValidationOptions defines validation options
type ValidationOptions struct {
	Scope                 *ValidationScope `json:"scope,omitempty"`
	Nonce                 *string          `json:"nonce,omitempty"`
	AutoRegisterMachine   bool             `json:"auto_register_machine,omitempty"`
	SkipMachineValidation bool             `json:"skip_machine_validation,omitempty"`
}

// === MAIN VALIDATION METHOD - MAPPING FROM RUBY ===

// ValidateLicense validates license with full business logic (Ruby: LicenseValidationService.call)
// Tận dụng Go: context for cancellation, structured error handling, type safety
func (vs *ValidationService) ValidateLicense(ctx context.Context, licenseKey string, options *ValidationOptions) (*ValidationResult, error) {
	// Set default options if nil
	if options == nil {
		options = &ValidationOptions{}
	}
	result := &ValidationResult{
		Timestamp: time.Now(),
		Scope:     options.Scope,
		Nonce:     options.Nonce,
	}

	// Step 1: Find license by key (Ruby: license lookup)
	license, err := vs.licenseRepo.GetByKey(ctx, licenseKey)
	if err != nil {
		result.Valid = false
		result.Code = ValidationCodeNotFound
		result.Detail = "does not exist"
		return result, nil
	}
	result.License = license

	// Step 2: Basic license validation (Ruby: basic validations)
	if valid, code, detail := vs.validateBasicLicense(ctx, license); !valid {
		result.Valid = false
		result.Code = code
		result.Detail = detail
		return result, nil
	}

	// Step 3: Policy validation (Ruby: policy checks)
	policyUUID, err := uuid.Parse(license.PolicyID)
	if err != nil {
		result.Valid = false
		result.Code = ValidationCodeNotFound
		result.Detail = "invalid policy ID"
		return result, nil
	}

	policy, err := vs.policyRepo.GetByID(ctx, policyUUID)
	if err != nil {
		result.Valid = false
		result.Code = ValidationCodeNotFound
		result.Detail = "policy not found"
		return result, nil
	}

	// Step 4: Machine validation if required (Ruby: machine validation)
	if !options.SkipMachineValidation && options.Scope != nil && options.Scope.Fingerprint != nil {
		machine, valid, code, detail := vs.validateMachine(ctx, license, policy, *options.Scope.Fingerprint, options.AutoRegisterMachine)
		if !valid {
			result.Valid = false
			result.Code = code
			result.Detail = detail
			return result, nil
		}
		result.Machine = machine
	}

	// Step 5: Resource limits validation (Ruby: resource validation)
	if valid, code, detail := vs.validateResourceLimits(ctx, license, policy); !valid {
		result.Valid = false
		result.Code = code
		result.Detail = detail
		return result, nil
	}

	// Step 6: Final expiry check (Ruby: final expiry check)
	if license.IsExpired() && !license.AllowAccessWithPolicy(policy) && !license.MaintainAccessWithPolicy(policy) {
		result.Valid = false
		result.Code = ValidationCodeExpired
		result.Detail = "is expired"
		return result, nil
	}

	// All validations passed
	result.Valid = true
	result.Code = ValidationCodeValid
	result.Detail = "is valid"

	return result, nil
}

// === HELPER METHODS ===

// isLicenseBanned checks if license is banned by checking owner status (Ruby: banned?)
// Ruby logic: return false if user_id.nil? || owner.nil?; owner.banned?
func (vs *ValidationService) isLicenseBanned(ctx context.Context, license *entities.License) (bool, error) {
	// Ruby: return false if user_id.nil?
	if !license.HasUserID() {
		return false, nil
	}

	// Load owner to check banned status
	ownerUUID, err := uuid.Parse(*license.UserID)
	if err != nil {
		return false, err // Invalid UUID format
	}

	owner, err := vs.userRepo.GetByID(ctx, ownerUUID)
	if err != nil {
		// If user not found, treat as owner.nil? = true -> return false
		// This matches Ruby behavior where missing owner returns false
		return false, nil
	}

	// Ruby: owner.nil? check
	if owner == nil {
		return false, nil
	}

	// Ruby: owner.banned?
	return owner.IsBanned(), nil
}

// === BASIC LICENSE VALIDATION - MAPPING FROM RUBY ===

// validateBasicLicense performs basic license validation (Ruby: basic validations)
func (vs *ValidationService) validateBasicLicense(ctx context.Context, license *entities.License) (bool, ValidationCode, string) {
	// Check if license is banned (Ruby: banned? check)
	// Ruby logic: return false if user_id.nil? || owner.nil?; owner.banned?
	if isBanned, err := vs.isLicenseBanned(ctx, license); err != nil {
		// If we can't check banned status, continue with validation (non-blocking)
	} else if isBanned {
		return false, ValidationCodeBanned, "is banned"
	}

	// Check if license is suspended (Ruby: suspended? check)
	if license.IsSuspended() {
		return false, ValidationCodeSuspended, "is suspended"
	}

	// Check if license is overdue for check-in (Ruby: check_in_overdue? check)
	// Get policy for check-in requirements
	policyUUID, err := uuid.Parse(license.PolicyID)
	if err == nil {
		policy, err := vs.policyRepo.GetByID(context.Background(), policyUUID)
		if err == nil {
			requiresCheckIn := policy.RequireCheckIn
			// Convert check-in interval from string to int64 (seconds)
			var intervalSeconds *int64
			if policy.CheckInInterval != nil && policy.CheckInIntervalCount != nil {
				// Simple conversion: assume interval is in seconds for now
				seconds := int64(*policy.CheckInIntervalCount)
				intervalSeconds = &seconds
			}
			if license.IsCheckInOverdue(intervalSeconds, requiresCheckIn) {
				return false, ValidationCodeOverdue, "is overdue for check in"
			}
		}
	}

	// Check expiry with revoke access logic (Ruby: revoke_access? && expired?)
	// Need to get policy for revoke access check
	policyUUID2, err2 := uuid.Parse(license.PolicyID)
	if err2 == nil {
		policy2, err2 := vs.policyRepo.GetByID(context.Background(), policyUUID2)
		if err2 == nil && license.RevokeAccessWithPolicy(policy2) && license.IsExpired() {
			return false, ValidationCodeExpired, "is expired"
		}
	}

	return true, ValidationCodeValid, "basic validation passed"
}

// === MACHINE VALIDATION - MAPPING FROM RUBY ===

// validateMachine validates machine requirements (Ruby: machine validation)
func (vs *ValidationService) validateMachine(ctx context.Context, license *entities.License, policy *entities.Policy, fingerprint string, autoRegister bool) (*entities.Machine, bool, ValidationCode, string) {
	// Check if policy is strict (requires machine tracking)
	if !policy.IsStrict() {
		return nil, true, ValidationCodeValid, "policy is not strict"
	}

	// Check if license is node-locked and has no machines
	if policy.IsNodeLocked() {
		machineCount, err := vs.machineRepo.Count(ctx, repositories.ListFilter{
			Filters: map[string]any{"license_id": license.ID},
		})
		if err != nil {
			return nil, false, ValidationCodeNotFound, "failed to count machines"
		}

		if machineCount == 0 {
			if autoRegister {
				// Auto-register machine (Go enhancement)
				machine := &entities.Machine{
					LicenseID:   license.ID,
					PolicyID:    license.PolicyID,
					Fingerprint: fingerprint,
					Status:      entities.MachineStatusActive,
				}
				if err := vs.machineRepo.Create(ctx, machine); err != nil {
					return nil, false, ValidationCodeNotFound, "failed to register machine"
				}
				return machine, true, ValidationCodeValid, "machine auto-registered"
			}
			return nil, false, ValidationCodeNoMachine, "must have exactly 1 associated machine"
		}
	}

	// Parse license ID to UUID
	licenseUUID, err := uuid.Parse(license.ID)
	if err != nil {
		return nil, false, ValidationCodeNotFound, "invalid license ID"
	}

	// Find machine by fingerprint
	machine, err := vs.machineRepo.GetByFingerprint(ctx, fingerprint, licenseUUID)
	if err != nil {
		if autoRegister {
			// Auto-register machine (Go enhancement)
			machine := &entities.Machine{
				LicenseID:   license.ID,
				PolicyID:    license.PolicyID,
				Fingerprint: fingerprint,
				Status:      entities.MachineStatusActive,
			}
			if err := vs.machineRepo.Create(ctx, machine); err != nil {
				return nil, false, ValidationCodeNotFound, "failed to register machine"
			}
			return machine, true, ValidationCodeValid, "machine auto-registered"
		}
		return nil, false, ValidationCodeFingerprintMismatch, "machine fingerprint not found"
	}

	// Validate machine is active
	if !machine.IsActive() {
		return nil, false, ValidationCodeFingerprintMismatch, "machine is inactive"
	}

	return machine, true, ValidationCodeValid, "machine validation passed"
}

// === RESOURCE LIMITS VALIDATION - MAPPING FROM RUBY ===

// validateResourceLimits validates resource limits (Ruby: resource validation)
func (vs *ValidationService) validateResourceLimits(ctx context.Context, license *entities.License, policy *entities.Policy) (bool, ValidationCode, string) {
	// Validate machine limits (Ruby: machine overage validation)
	if valid, code, detail := vs.validateMachineLimits(ctx, license, policy); !valid {
		return false, code, detail
	}

	// Validate core limits (Ruby: core overage validation)
	if valid, code, detail := vs.validateCoreLimits(ctx, license, policy); !valid {
		return false, code, detail
	}

	// Validate process limits (Ruby: process overage validation)
	if valid, code, detail := vs.validateProcessLimits(ctx, license, policy); !valid {
		return false, code, detail
	}

	return true, ValidationCodeValid, "resource validation passed"
}

// validateMachineLimits validates machine count limits (Ruby: machine overage)
func (vs *ValidationService) validateMachineLimits(ctx context.Context, license *entities.License, policy *entities.Policy) (bool, ValidationCode, string) {
	// Get effective max machines (license override or policy default)
	maxMachines := license.GetMaxMachines(policy.MaxMachines)
	if maxMachines == nil || *maxMachines <= 0 {
		return true, ValidationCodeValid, "no machine limit"
	}

	machineCount, err := vs.machineRepo.Count(ctx, repositories.ListFilter{
		Filters: map[string]interface{}{"license_id": license.ID},
	})
	if err != nil {
		return false, ValidationCodeNotFound, "failed to count machines"
	}

	if int(machineCount) <= *maxMachines {
		return true, ValidationCodeValid, "within machine limit"
	}

	// Check overage allowance (Ruby: overage strategies)
	if license.AlwaysAllowOverageWithPolicy(policy) ||
		(license.Allow125xOverageWithPolicy(policy) && int(machineCount) <= int(float64(*maxMachines)*1.25)) ||
		(license.Allow15xOverageWithPolicy(policy) && int(machineCount) <= int(float64(*maxMachines)*1.5)) ||
		(license.Allow2xOverageWithPolicy(policy) && int(machineCount) <= (*maxMachines)*2) {
		return true, ValidationCodeValid, "within overage allowance"
	}

	return false, ValidationCodeTooManyMachines, "has too many associated machines"
}

// validateCoreLimits validates core count limits (Ruby: core overage)
func (vs *ValidationService) validateCoreLimits(ctx context.Context, license *entities.License, policy *entities.Policy) (bool, ValidationCode, string) {
	// Get effective max cores (license override or policy default)
	maxCores := license.GetMaxCores(policy.MaxCores)
	if maxCores == nil || *maxCores <= 0 {
		return true, ValidationCodeValid, "no core limit"
	}

	// Check current core usage from license.MachinesCoreCount
	if license.MachinesCoreCount <= *maxCores {
		return true, ValidationCodeValid, "within core limit"
	}

	// Check overage allowance (Ruby: overage strategies)
	if license.AlwaysAllowOverageWithPolicy(policy) ||
		(license.Allow125xOverageWithPolicy(policy) && license.MachinesCoreCount <= int(float64(*maxCores)*1.25)) ||
		(license.Allow15xOverageWithPolicy(policy) && license.MachinesCoreCount <= int(float64(*maxCores)*1.5)) ||
		(license.Allow2xOverageWithPolicy(policy) && license.MachinesCoreCount <= (*maxCores)*2) {
		return true, ValidationCodeValid, "within core overage allowance"
	}

	return false, ValidationCodeTooManyCores, "has too many allocated cores"
}

// validateProcessLimits validates process count limits (Ruby: process overage)
func (vs *ValidationService) validateProcessLimits(ctx context.Context, license *entities.License, policy *entities.Policy) (bool, ValidationCode, string) {
	// Get effective max processes (license override or policy default)
	maxProcesses := license.GetMaxProcesses(policy.MaxProcesses)
	if maxProcesses == nil || *maxProcesses <= 0 {
		return true, ValidationCodeValid, "no process limit"
	}

	// Count processes from machines (Ruby: processes.count)
	processCount, err := vs.countProcessesByLicense(ctx, license.ID)
	if err != nil {
		return false, ValidationCodeNotFound, "failed to count processes"
	}

	if processCount <= *maxProcesses {
		return true, ValidationCodeValid, "within process limit"
	}

	// Check overage allowance (Ruby: overage strategies)
	if license.AlwaysAllowOverageWithPolicy(policy) ||
		(license.Allow125xOverageWithPolicy(policy) && processCount <= int(float64(*maxProcesses)*1.25)) ||
		(license.Allow15xOverageWithPolicy(policy) && processCount <= int(float64(*maxProcesses)*1.5)) ||
		(license.Allow2xOverageWithPolicy(policy) && processCount <= (*maxProcesses)*2) {
		return true, ValidationCodeValid, "within process overage allowance"
	}

	return false, ValidationCodeTooManyProcesses, "has too many associated processes"
}

// === HELPER METHODS ===

// countProcessesByLicense counts total processes for a license (Ruby: processes.count)
func (vs *ValidationService) countProcessesByLicense(ctx context.Context, licenseID string) (int, error) {
	// Get all machines for this license
	machines, _, err := vs.machineRepo.List(ctx, repositories.ListFilter{
		Filters: map[string]any{"license_id": licenseID},
	})
	if err != nil {
		return 0, err
	}

	// Count processes from all machines
	// For now, assume 1 process per active machine (Ruby: processes.count)
	totalProcesses := 0
	for _, machine := range machines {
		if machine.IsActive() {
			totalProcesses += 1
		}
	}

	return totalProcesses, nil
}
