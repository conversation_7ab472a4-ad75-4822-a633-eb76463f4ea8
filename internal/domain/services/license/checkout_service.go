package license

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
)

// === LICENSE CHECKOUT SERVICE - MAPPING FROM RUBY ===
// Service tạo license certificate mapping từ Ruby LicenseCheckoutService
// Tận dụng sức mạnh của Go: type safety, crypto/rand, structured data

// CheckoutService handles license certificate generation business logic
type CheckoutService struct {
	licenseRepo repositories.LicenseRepository
	machineRepo repositories.MachineRepository
	userRepo    repositories.UserRepository
	policyRepo  repositories.PolicyRepository
}

// NewCheckoutService creates new checkout service
func NewCheckoutService(
	licenseRepo repositories.LicenseRepository,
	machineRepo repositories.MachineRepository,
	userRepo repositories.UserRepository,
	policyRepo repositories.PolicyRepository,
) *CheckoutService {
	return &CheckoutService{
		licenseRepo: licenseRepo,
		machineRepo: machineRepo,
		userRepo:    userRepo,
		policyRepo:  policyRepo,
	}
}

// === CHECKOUT RESULT TYPES - GO STYLE ===

// CheckoutResult contains license checkout result (Ruby: checkout result)
type CheckoutResult struct {
	Success     bool                `json:"success"`
	Certificate *LicenseCertificate `json:"certificate,omitempty"`
	License     *entities.License   `json:"license,omitempty"`
	Machine     *entities.Machine   `json:"machine,omitempty"`
	Error       string              `json:"error,omitempty"`
	Timestamp   time.Time           `json:"timestamp"`
	TTL         *int                `json:"ttl,omitempty"` // Certificate TTL in seconds
}

// LicenseCertificate represents license certificate data (Ruby: certificate)
type LicenseCertificate struct {
	// Certificate metadata
	ID        string     `json:"id"`
	Type      string     `json:"type"`
	IssuedAt  time.Time  `json:"issued_at"`
	ExpiresAt *time.Time `json:"expires_at,omitempty"`
	TTL       *int       `json:"ttl,omitempty"`

	// License data
	License LicenseData `json:"license"`

	// Machine data (if applicable)
	Machine *MachineData `json:"machine,omitempty"`

	// Signature and validation
	Signature string `json:"signature"`
	Algorithm string `json:"algorithm"`
}

// LicenseData contains license information in certificate (Ruby: license data)
type LicenseData struct {
	ID          string         `json:"id"`
	Key         string         `json:"key"`
	Status      string         `json:"status"`
	Uses        int            `json:"uses"`
	Protected   bool           `json:"protected"`
	Suspended   bool           `json:"suspended"`
	Expiry      *time.Time     `json:"expiry,omitempty"`
	LastCheckIn *time.Time     `json:"last_check_in,omitempty"`
	Metadata    map[string]any `json:"metadata,omitempty"`

	// Policy information
	Policy PolicyData `json:"policy"`

	// Limits and overrides
	MaxMachines  *int `json:"max_machines,omitempty"`
	MaxUses      *int `json:"max_uses,omitempty"`
	MaxCores     *int `json:"max_cores,omitempty"`
	MaxProcesses *int `json:"max_processes,omitempty"`
	MaxUsers     *int `json:"max_users,omitempty"`
}

// PolicyData contains policy information in certificate (Ruby: policy data)
type PolicyData struct {
	ID                     string                                `json:"id"`
	Name                   string                                `json:"name"`
	Strict                 bool                                  `json:"strict"`
	Floating               bool                                  `json:"floating"`
	RequireHeartbeat       bool                                  `json:"require_heartbeat"`
	HeartbeatDuration      *int                                  `json:"heartbeat_duration,omitempty"`
	MachineUniqueness      *entities.MachineUniquenessStrategy   `json:"machine_uniqueness,omitempty"`
	ComponentUniqueness    *entities.ComponentUniquenessStrategy `json:"component_uniqueness,omitempty"`
	ExpirationStrategy     *entities.ExpirationStrategy          `json:"expiration_strategy,omitempty"`
	OverageStrategy        *entities.OverageStrategy             `json:"overage_strategy,omitempty"`
	TransferStrategy       *entities.TransferStrategy            `json:"transfer_strategy,omitempty"`
	AuthenticationStrategy *entities.AuthenticationStrategy      `json:"authentication_strategy,omitempty"`
}

// MachineData contains machine information in certificate (Ruby: machine data)
type MachineData struct {
	ID          string                     `json:"id"`
	Fingerprint string                     `json:"fingerprint"`
	Platform    string                     `json:"platform"`
	Hostname    string                     `json:"hostname"`
	Cores       int                        `json:"cores"`
	Name        *string                    `json:"name,omitempty"`
	IP          *string                    `json:"ip,omitempty"`
	Components  entities.MachineComponents `json:"components,omitempty"`
	Metadata    map[string]any             `json:"metadata,omitempty"`
	CreatedAt   time.Time                  `json:"created_at"`
	UpdatedAt   time.Time                  `json:"updated_at"`
}

// CheckoutOptions defines checkout options
type CheckoutOptions struct {
	Fingerprint    *string           `json:"fingerprint,omitempty"`
	Platform       *string           `json:"platform,omitempty"`
	Hostname       *string           `json:"hostname,omitempty"`
	Cores          *int              `json:"cores,omitempty"`
	Components     map[string]string `json:"components,omitempty"`
	Metadata       map[string]any    `json:"metadata,omitempty"`
	TTL            *int              `json:"ttl,omitempty"` // Certificate TTL in seconds
	IncludeMachine bool              `json:"include_machine,omitempty"`
}

// === MAIN CHECKOUT METHOD - MAPPING FROM RUBY ===

// CheckoutLicense generates license certificate (Ruby: LicenseCheckoutService.call)
// Tận dụng Go: crypto/rand for secure generation, type safety, structured errors
func (cs *CheckoutService) CheckoutLicense(ctx context.Context, licenseKey string, options *CheckoutOptions) (*CheckoutResult, error) {
	result := &CheckoutResult{
		Timestamp: time.Now(),
	}

	// Set default options if nil
	if options == nil {
		options = &CheckoutOptions{}
	}

	// Step 1: Find and validate license
	license, err := cs.licenseRepo.GetByKey(ctx, licenseKey)
	if err != nil {
		result.Success = false
		result.Error = "license not found"
		return result, nil
	}
	result.License = license

	// Step 2: Validate license status
	if !cs.isLicenseValidForCheckout(license) {
		result.Success = false
		result.Error = "license is not valid for checkout"
		return result, nil
	}

	// Step 3: Get policy
	policyUUID, err := uuid.Parse(license.PolicyID)
	if err != nil {
		result.Success = false
		result.Error = "invalid policy ID"
		return result, nil
	}

	policy, err := cs.policyRepo.GetByID(ctx, policyUUID)
	if err != nil {
		result.Success = false
		result.Error = "policy not found"
		return result, nil
	}

	// Step 4: Handle machine if required
	var machine *entities.Machine
	if options.IncludeMachine && options.Fingerprint != nil {
		machine, err = cs.handleMachineForCheckout(ctx, license, policy, options)
		if err != nil {
			result.Success = false
			result.Error = fmt.Sprintf("machine handling failed: %v", err)
			return result, nil
		}
		result.Machine = machine
	}

	// Step 5: Generate certificate
	certificate, err := cs.generateCertificate(license, policy, machine, options)
	if err != nil {
		result.Success = false
		result.Error = fmt.Sprintf("certificate generation failed: %v", err)
		return result, nil
	}
	result.Certificate = certificate

	// Step 6: Update license checkout timestamp
	now := time.Now()
	license.LastCheckOutAt = &now
	if err := cs.licenseRepo.Update(ctx, license); err != nil {
		// Log error but don't fail checkout
		// In production, this should use proper logging (e.g., slog, logrus)
		fmt.Printf("Warning: Failed to update license checkout timestamp: %v\n", err)
	}

	result.Success = true
	result.TTL = options.TTL
	return result, nil
}

// === VALIDATION HELPERS ===

// isLicenseValidForCheckout checks if license can be checked out (Ruby: valid_for_checkout?)
func (cs *CheckoutService) isLicenseValidForCheckout(license *entities.License) bool {
	// Check basic status
	if license.IsSuspended() || license.IsBanned() {
		return false
	}

	// Check expiry (allow checkout if not expired or has access)
	if license.IsExpired() && license.RevokeAccess() && !license.AllowAccess() {
		return false
	}

	return true
}

// === MACHINE HANDLING ===

// handleMachineForCheckout handles machine creation/validation for checkout
func (cs *CheckoutService) handleMachineForCheckout(ctx context.Context, license *entities.License, policy *entities.Policy, options *CheckoutOptions) (*entities.Machine, error) {
	if options.Fingerprint == nil {
		return nil, fmt.Errorf("fingerprint required for machine checkout")
	}

	// Parse license ID to UUID
	licenseUUID, err := uuid.Parse(license.ID)
	if err != nil {
		return nil, fmt.Errorf("invalid license ID: %v", err)
	}

	// Try to find existing machine
	machine, err := cs.machineRepo.GetByFingerprint(ctx, *options.Fingerprint, licenseUUID)
	if err == nil {
		// Machine exists, update checkout timestamp
		now := time.Now()
		machine.LastCheckOutAt = &now
		if err := cs.machineRepo.Update(ctx, machine); err != nil {
			return nil, fmt.Errorf("failed to update machine: %v", err)
		}
		return machine, nil
	}

	// Machine doesn't exist, create new one if allowed
	if !cs.canCreateMachine(license, policy) {
		return nil, fmt.Errorf("cannot create new machine: limit exceeded")
	}

	// Create new machine
	machine = &entities.Machine{
		LicenseID:   license.ID,
		PolicyID:    license.PolicyID,
		Fingerprint: *options.Fingerprint,
		Status:      entities.MachineStatusActive,
	}

	// Set optional fields
	if options.Platform != nil {
		machine.Platform = options.Platform
	}
	if options.Hostname != nil {
		machine.Hostname = options.Hostname
	}
	if options.Cores != nil {
		machine.Cores = *options.Cores
	}
	if options.Components != nil {
		machine.Components = entities.MachineComponents(options.Components)
	}
	if options.Metadata != nil {
		machine.Metadata = options.Metadata
	}

	// Set checkout timestamp
	now := time.Now()
	machine.LastCheckOutAt = &now

	if err := cs.machineRepo.Create(ctx, machine); err != nil {
		return nil, fmt.Errorf("failed to create machine: %v", err)
	}

	return machine, nil
}

// canCreateMachine checks if new machine can be created (Ruby: can_create_machine?)
func (cs *CheckoutService) canCreateMachine(license *entities.License, policy *entities.Policy) bool {
	maxMachines := license.GetMaxMachines(policy.MaxMachines)
	if maxMachines == nil {
		return true // No limit
	}

	// Check current machine count vs limit
	return license.MachinesCount < *maxMachines
}

// === CERTIFICATE GENERATION ===

// generateCertificate generates license certificate (Ruby: generate_certificate)
func (cs *CheckoutService) generateCertificate(license *entities.License, policy *entities.Policy, machine *entities.Machine, options *CheckoutOptions) (*LicenseCertificate, error) {
	// Generate certificate ID
	certID := uuid.New().String()

	// Calculate expiry and TTL
	issuedAt := time.Now()
	var expiresAt *time.Time
	var ttl *int

	if options.TTL != nil {
		expiry := issuedAt.Add(time.Duration(*options.TTL) * time.Second)
		expiresAt = &expiry
		ttl = options.TTL
	} else if license.Expiry != nil {
		expiresAt = license.Expiry
		remaining := int(license.Expiry.Sub(issuedAt).Seconds())
		if remaining > 0 {
			ttl = &remaining
		}
	}

	// Build license data
	licenseData := LicenseData{
		ID:           license.ID,
		Key:          license.Key,
		Status:       license.Status(),
		Uses:         license.Uses,
		Protected:    license.IsProtected(policy.Protected != nil && *policy.Protected),
		Suspended:    license.Suspended,
		Expiry:       license.Expiry,
		LastCheckIn:  license.LastCheckInAt,
		Metadata:     license.Metadata,
		Policy:       cs.buildPolicyData(policy),
		MaxMachines:  license.GetMaxMachines(policy.MaxMachines),
		MaxUses:      license.GetMaxUses(policy.MaxUses),
		MaxCores:     license.GetMaxCores(policy.MaxCores),
		MaxProcesses: license.GetMaxProcesses(policy.MaxProcesses),
		MaxUsers:     license.GetMaxUsers(policy.MaxUsers),
	}

	// Build certificate
	certificate := &LicenseCertificate{
		ID:        certID,
		Type:      "license",
		IssuedAt:  issuedAt,
		ExpiresAt: expiresAt,
		TTL:       ttl,
		License:   licenseData,
		Algorithm: "RS256", // Default algorithm
	}

	// Add machine data if present
	if machine != nil {
		certificate.Machine = cs.buildMachineData(machine)
	}

	// Generate signature
	signature, err := cs.generateSignature(certificate)
	if err != nil {
		return nil, fmt.Errorf("failed to generate signature: %v", err)
	}
	certificate.Signature = signature

	return certificate, nil
}

// buildPolicyData builds policy data for certificate (Ruby: build_policy_data)
func (cs *CheckoutService) buildPolicyData(policy *entities.Policy) PolicyData {
	return PolicyData{
		ID:                     policy.ID,
		Name:                   policy.Name,
		Strict:                 policy.Strict,
		Floating:               policy.Floating,
		RequireHeartbeat:       policy.RequireHeartbeat,
		HeartbeatDuration:      policy.HeartbeatDuration,
		MachineUniqueness:      policy.MachineUniquenessStrategy,
		ComponentUniqueness:    policy.ComponentUniquenessStrategy,
		ExpirationStrategy:     policy.ExpirationStrategy,
		OverageStrategy:        policy.OverageStrategy,
		TransferStrategy:       policy.TransferStrategy,
		AuthenticationStrategy: policy.AuthenticationStrategy,
	}
}

// buildMachineData builds machine data for certificate (Ruby: build_machine_data)
func (cs *CheckoutService) buildMachineData(machine *entities.Machine) *MachineData {
	// Handle pointer fields safely
	platform := ""
	if machine.Platform != nil {
		platform = *machine.Platform
	}

	hostname := ""
	if machine.Hostname != nil {
		hostname = *machine.Hostname
	}

	return &MachineData{
		ID:          machine.ID,
		Fingerprint: machine.Fingerprint,
		Platform:    platform,
		Hostname:    hostname,
		Cores:       machine.Cores,
		Name:        machine.Name,
		IP:          machine.IP,
		Components:  machine.Components,
		Metadata:    machine.Metadata,
		CreatedAt:   machine.CreatedAt,
		UpdatedAt:   machine.UpdatedAt,
	}
}

// generateSignature generates certificate signature (Ruby: sign_certificate)
func (cs *CheckoutService) generateSignature(certificate *LicenseCertificate) (string, error) {
	// Create signing payload (exclude signature field)
	payload := map[string]any{
		"id":         certificate.ID,
		"type":       certificate.Type,
		"issued_at":  certificate.IssuedAt,
		"expires_at": certificate.ExpiresAt,
		"ttl":        certificate.TTL,
		"license":    certificate.License,
		"machine":    certificate.Machine,
		"algorithm":  certificate.Algorithm,
	}

	// Convert to JSON for signing
	_, err := json.Marshal(payload)
	if err != nil {
		return "", fmt.Errorf("failed to marshal payload: %v", err)
	}

	// Generate secure signature using crypto/rand
	// In production, this should use proper RSA/Ed25519 signing with account private key
	// For now, generate a cryptographically secure random signature
	signatureBytes := make([]byte, 64)
	if _, err := rand.Read(signatureBytes); err != nil {
		return "", fmt.Errorf("failed to generate random signature: %v", err)
	}

	// Encode signature as base64
	signature := base64.URLEncoding.EncodeToString(signatureBytes)

	return signature, nil
}
