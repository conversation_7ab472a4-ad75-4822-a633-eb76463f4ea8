package license

import (
	"context"
	"fmt"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
)

// === LICENSE MANAGEMENT SERVICE - MAPPING FROM RUBY ===
// Service quản lý license mapping từ Ruby License model business methods
// Tận dụng sức mạnh của Go: type safety, transaction handling, structured operations

// ManagementService handles license management business logic
type ManagementService struct {
	licenseRepo repositories.LicenseRepository
	machineRepo repositories.MachineRepository
	userRepo    repositories.UserRepository
	policyRepo  repositories.PolicyRepository
}

// NewManagementService creates new management service
func NewManagementService(
	licenseRepo repositories.LicenseRepository,
	machineRepo repositories.MachineRepository,
	userRepo repositories.UserRepository,
	policyRepo repositories.PolicyRepository,
) *ManagementService {
	return &ManagementService{
		licenseRepo: licenseRepo,
		machineRepo: machineRepo,
		userRepo:    userRepo,
		policyRepo:  policyRepo,
	}
}

// === MANAGEMENT RESULT TYPES - GO STYLE ===

// ManagementResult contains license management operation result
type ManagementResult struct {
	Success   bool              `json:"success"`
	License   *entities.License `json:"license,omitempty"`
	Message   string            `json:"message"`
	Error     string            `json:"error,omitempty"`
	Timestamp time.Time         `json:"timestamp"`
}

// === LICENSE CREATION ===

// CreateLicense creates new license with validation (Ruby: License.create!)
func (ms *ManagementService) CreateLicense(ctx context.Context, req *CreateLicenseRequest) (*ManagementResult, error) {
	result := &ManagementResult{
		Timestamp: time.Now(),
	}

	// Validate policy exists
	policyUUID, err := uuid.Parse(req.PolicyID)
	if err != nil {
		result.Error = "invalid policy ID"
		return result, nil
	}

	policy, err := ms.policyRepo.GetByID(ctx, policyUUID)
	if err != nil {
		result.Error = "policy not found"
		return result, nil
	}

	// Create license entity
	license := &entities.License{
		ID:                   uuid.New().String(),
		Key:                  req.Key,
		PolicyID:             req.PolicyID,
		OrganizationID:       req.OrganizationID,
		UserID:               req.UserID,
		Name:                 req.Name,
		Uses:                 0,
		Suspended:            false,
		Protected:            req.Protected,
		MaxMachinesOverride:  req.MaxMachinesOverride,
		MaxUsesOverride:      req.MaxUsesOverride,
		MaxCoresOverride:     req.MaxCoresOverride,
		MaxProcessesOverride: req.MaxProcessesOverride,
		MaxUsersOverride:     req.MaxUsersOverride,
		Metadata:             req.Metadata,
		CreatedAt:            time.Now(),
		UpdatedAt:            time.Now(),
	}

	// Generate key if not provided
	if license.Key == "" {
		license.Key = license.GenerateDefaultKey()
	}

	// Set default values
	license.SetDefaultValues()

	// Validate license
	if err := license.ValidateBasicFields(); err != nil {
		result.Error = fmt.Sprintf("validation failed: %v", err)
		return result, nil
	}

	// Set expiry based on policy
	var duration *int64
	if policy.Duration != nil {
		d := int64(*policy.Duration)
		duration = &d
	}
	license.SetExpiryOnCreation(duration)

	// Create in database
	if err := ms.licenseRepo.Create(ctx, license); err != nil {
		result.Error = fmt.Sprintf("failed to create license: %v", err)
		return result, nil
	}

	result.Success = true
	result.License = license
	result.Message = "license created successfully"
	return result, nil
}

// CreateLicenseRequest defines license creation request
type CreateLicenseRequest struct {
	Key                  string         `json:"key,omitempty"`
	PolicyID             string         `json:"policy_id"`
	OrganizationID       string         `json:"organization_id"`
	UserID               *string        `json:"user_id,omitempty"`
	Name                 *string        `json:"name,omitempty"`
	Protected            bool           `json:"protected,omitempty"`
	MaxMachinesOverride  *int           `json:"max_machines_override,omitempty"`
	MaxUsesOverride      *int           `json:"max_uses_override,omitempty"`
	MaxCoresOverride     *int           `json:"max_cores_override,omitempty"`
	MaxProcessesOverride *int           `json:"max_processes_override,omitempty"`
	MaxUsersOverride     *int           `json:"max_users_override,omitempty"`
	Metadata             map[string]any `json:"metadata,omitempty"`
}

// === LICENSE SUSPENSION ===

// SuspendLicense suspends a license (Ruby: license.suspend!)
func (ms *ManagementService) SuspendLicense(ctx context.Context, licenseKey string) (*ManagementResult, error) {
	result := &ManagementResult{
		Timestamp: time.Now(),
	}

	// Find license
	license, err := ms.licenseRepo.GetByKey(ctx, licenseKey)
	if err != nil {
		result.Error = "license not found"
		return result, nil
	}

	// Check if already suspended
	if license.IsSuspended() {
		result.Error = "license is already suspended"
		return result, nil
	}

	// Suspend license
	license.Suspend()

	// Update in database
	if err := ms.licenseRepo.Update(ctx, license); err != nil {
		result.Error = fmt.Sprintf("failed to suspend license: %v", err)
		return result, nil
	}

	result.Success = true
	result.License = license
	result.Message = "license suspended successfully"
	return result, nil
}

// ReinstateLicense reinstates a suspended license (Ruby: license.reinstate!)
func (ms *ManagementService) ReinstateLicense(ctx context.Context, licenseKey string) (*ManagementResult, error) {
	result := &ManagementResult{
		Timestamp: time.Now(),
	}

	// Find license
	license, err := ms.licenseRepo.GetByKey(ctx, licenseKey)
	if err != nil {
		result.Error = "license not found"
		return result, nil
	}

	// Check if not suspended
	if !license.IsSuspended() {
		result.Error = "license is not suspended"
		return result, nil
	}

	// Reinstate license
	license.Reinstate()

	// Update in database
	if err := ms.licenseRepo.Update(ctx, license); err != nil {
		result.Error = fmt.Sprintf("failed to reinstate license: %v", err)
		return result, nil
	}

	result.Success = true
	result.License = license
	result.Message = "license reinstated successfully"
	return result, nil
}

// === LICENSE RENEWAL ===

// RenewLicense renews a license (Ruby: license.renew!)
func (ms *ManagementService) RenewLicense(ctx context.Context, licenseKey string) (*ManagementResult, error) {
	result := &ManagementResult{
		Timestamp: time.Now(),
	}

	// Find license
	license, err := ms.licenseRepo.GetByKey(ctx, licenseKey)
	if err != nil {
		result.Error = "license not found"
		return result, nil
	}

	// Get policy
	policyUUID, err := uuid.Parse(license.PolicyID)
	if err != nil {
		result.Error = "invalid policy ID"
		return result, nil
	}

	policy, err := ms.policyRepo.GetByID(ctx, policyUUID)
	if err != nil {
		result.Error = "policy not found"
		return result, nil
	}

	// Check if license can be renewed
	var duration *int64
	if policy.Duration != nil {
		d := int64(*policy.Duration)
		duration = &d
	}
	if !license.CanBeRenewed(duration) {
		result.Error = "license cannot be renewed"
		return result, nil
	}

	// Renew license
	renewalStrategy := "RENEW_FROM_EXPIRY" // Default strategy
	if policy.RenewalBasis != nil {
		switch *policy.RenewalBasis {
		case entities.RenewalFromNow:
			renewalStrategy = "RENEW_FROM_NOW"
		case entities.RenewalFromNowIfExpired:
			renewalStrategy = "RENEW_FROM_NOW_IF_EXPIRED"
		}
	}

	if err := license.Renew(duration, renewalStrategy); err != nil {
		result.Error = fmt.Sprintf("renewal failed: %v", err)
		return result, nil
	}

	// Update in database
	if err := ms.licenseRepo.Update(ctx, license); err != nil {
		result.Error = fmt.Sprintf("failed to update license: %v", err)
		return result, nil
	}

	result.Success = true
	result.License = license
	result.Message = "license renewed successfully"
	return result, nil
}

// === LICENSE TRANSFER ===

// TransferLicense transfers license to new policy (Ruby: license.transfer!)
func (ms *ManagementService) TransferLicense(ctx context.Context, licenseKey string, newPolicyID string) (*ManagementResult, error) {
	result := &ManagementResult{
		Timestamp: time.Now(),
	}

	// Find license
	license, err := ms.licenseRepo.GetByKey(ctx, licenseKey)
	if err != nil {
		result.Error = "license not found"
		return result, nil
	}

	// Validate new policy exists
	policyUUID, err := uuid.Parse(newPolicyID)
	if err != nil {
		result.Error = "invalid new policy ID"
		return result, nil
	}

	newPolicy, err := ms.policyRepo.GetByID(ctx, policyUUID)
	if err != nil {
		result.Error = "new policy not found"
		return result, nil
	}

	// Transfer license
	resetExpiry := newPolicy.TransferStrategy != nil && *newPolicy.TransferStrategy == entities.TransferResetExpiry
	var newDuration *int64
	if newPolicy.Duration != nil {
		d := int64(*newPolicy.Duration)
		newDuration = &d
	}
	license.Transfer(newPolicyID, resetExpiry, newDuration)

	// Update in database
	if err := ms.licenseRepo.Update(ctx, license); err != nil {
		result.Error = fmt.Sprintf("failed to transfer license: %v", err)
		return result, nil
	}

	result.Success = true
	result.License = license
	result.Message = "license transferred successfully"
	return result, nil
}

// === LICENSE CHECK-IN ===

// CheckInLicense performs license check-in (Ruby: license.check_in!)
func (ms *ManagementService) CheckInLicense(ctx context.Context, licenseKey string) (*ManagementResult, error) {
	result := &ManagementResult{
		Timestamp: time.Now(),
	}

	// Find license
	license, err := ms.licenseRepo.GetByKey(ctx, licenseKey)
	if err != nil {
		result.Error = "license not found"
		return result, nil
	}

	// Perform check-in
	license.CheckIn()

	// Set first check-in if needed
	license.SetFirstCheckIn()

	// Update in database
	if err := ms.licenseRepo.Update(ctx, license); err != nil {
		result.Error = fmt.Sprintf("failed to check in license: %v", err)
		return result, nil
	}

	result.Success = true
	result.License = license
	result.Message = "license checked in successfully"
	return result, nil
}

// === LICENSE USAGE ===

// IncrementUsage increments license usage (Ruby: license.increment_uses!)
func (ms *ManagementService) IncrementUsage(ctx context.Context, licenseKey string) (*ManagementResult, error) {
	result := &ManagementResult{
		Timestamp: time.Now(),
	}

	// Find license
	license, err := ms.licenseRepo.GetByKey(ctx, licenseKey)
	if err != nil {
		result.Error = "license not found"
		return result, nil
	}

	// Get policy for limits
	policyUUID, err := uuid.Parse(license.PolicyID)
	if err != nil {
		result.Error = "invalid policy ID"
		return result, nil
	}

	policy, err := ms.policyRepo.GetByID(ctx, policyUUID)
	if err != nil {
		result.Error = "policy not found"
		return result, nil
	}

	// Check if usage can be incremented
	maxUses := license.GetMaxUses(policy.MaxUses)
	if !license.CanIncrementUsage(maxUses) {
		result.Error = "usage limit exceeded"
		return result, nil
	}

	// Increment usage
	license.IncrementUsage()

	// Set expiry on first use if needed
	if license.Uses == 1 {
		var duration *int64
		if policy.Duration != nil {
			d := int64(*policy.Duration)
			duration = &d
		}
		license.SetExpiryOnFirstUse(duration)
	}

	// Update in database
	if err := ms.licenseRepo.Update(ctx, license); err != nil {
		result.Error = fmt.Sprintf("failed to increment usage: %v", err)
		return result, nil
	}

	result.Success = true
	result.License = license
	result.Message = "usage incremented successfully"
	return result, nil
}
