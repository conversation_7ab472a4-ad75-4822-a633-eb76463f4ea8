package license

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
)

// === LICENSE LOOKUP SERVICE - MAPPING FROM RUBY ===
// Service tìm kiếm license mapping từ Ruby LicenseKeyLookupService
// Tận dụng sức mạnh của Go: type safety, structured queries, efficient filtering

// LookupService handles license lookup business logic
type LookupService struct {
	licenseRepo repositories.LicenseRepository
	machineRepo repositories.MachineRepository
	userRepo    repositories.UserRepository
	policyRepo  repositories.PolicyRepository
}

// NewLookupService creates new lookup service
func NewLookupService(
	licenseRepo repositories.LicenseRepository,
	machineRepo repositories.MachineRepository,
	userRepo repositories.UserRepository,
	policyRepo repositories.PolicyRepository,
) *LookupService {
	return &LookupService{
		licenseRepo: licenseRepo,
		machineRepo: machineRepo,
		userRepo:    userRepo,
		policyRepo:  policyRepo,
	}
}

// === LOOKUP RESULT TYPES - GO STYLE ===

// LookupResult contains license lookup result (Ruby: lookup result)
type LookupResult struct {
	Found     bool                `json:"found"`
	License   *entities.License   `json:"license,omitempty"`
	Policy    *entities.Policy    `json:"policy,omitempty"`
	Machines  []*entities.Machine `json:"machines,omitempty"`
	Users     []*entities.User    `json:"users,omitempty"`
	Error     string              `json:"error,omitempty"`
	Timestamp time.Time           `json:"timestamp"`
}

// LookupOptions defines lookup options
type LookupOptions struct {
	IncludePolicy   bool `json:"include_policy,omitempty"`
	IncludeMachines bool `json:"include_machines,omitempty"`
	IncludeUsers    bool `json:"include_users,omitempty"`
	ValidateStatus  bool `json:"validate_status,omitempty"`
}

// === MAIN LOOKUP METHOD - MAPPING FROM RUBY ===

// LookupLicense finds license by key with optional related data (Ruby: LicenseKeyLookupService.call)
// Tận dụng Go: efficient preloading, type safety, structured options
func (ls *LookupService) LookupLicense(ctx context.Context, licenseKey string, options *LookupOptions) (*LookupResult, error) {
	result := &LookupResult{
		Timestamp: time.Now(),
	}

	// Set default options if nil
	if options == nil {
		options = &LookupOptions{}
	}

	// Step 1: Find license by key
	license, err := ls.licenseRepo.GetByKey(ctx, licenseKey)
	if err != nil {
		result.Found = false
		result.Error = "license not found"
		return result, nil
	}
	result.License = license
	result.Found = true

	// Step 2: Validate status if requested
	if options.ValidateStatus {
		if !ls.isLicenseValid(license) {
			result.Error = "license is not valid"
			return result, nil
		}
	}

	// Step 3: Load policy if requested
	if options.IncludePolicy {
		policy, err := ls.loadPolicy(ctx, license.PolicyID)
		if err != nil {
			result.Error = fmt.Sprintf("failed to load policy: %v", err)
			return result, nil
		}
		result.Policy = policy
	}

	// Step 4: Load machines if requested
	if options.IncludeMachines {
		machines, err := ls.loadMachines(ctx, license.ID)
		if err != nil {
			result.Error = fmt.Sprintf("failed to load machines: %v", err)
			return result, nil
		}
		result.Machines = machines
	}

	// Step 5: Load users if requested
	if options.IncludeUsers {
		users, err := ls.loadUsers(ctx, license.ID)
		if err != nil {
			result.Error = fmt.Sprintf("failed to load users: %v", err)
			return result, nil
		}
		result.Users = users
	}

	return result, nil
}

// === BATCH LOOKUP METHODS ===

// LookupMultipleLicenses finds multiple licenses by keys (Ruby: batch lookup)
func (ls *LookupService) LookupMultipleLicenses(ctx context.Context, licenseKeys []string, options *LookupOptions) ([]*LookupResult, error) {
	results := make([]*LookupResult, 0, len(licenseKeys))

	for _, key := range licenseKeys {
		result, err := ls.LookupLicense(ctx, key, options)
		if err != nil {
			// Add error result for this key
			results = append(results, &LookupResult{
				Found:     false,
				Error:     fmt.Sprintf("lookup failed: %v", err),
				Timestamp: time.Now(),
			})
			continue
		}
		results = append(results, result)
	}

	return results, nil
}

// === ADVANCED LOOKUP METHODS ===

// LookupByFingerprint finds licenses by machine fingerprint (Ruby: lookup by fingerprint)
func (ls *LookupService) LookupByFingerprint(ctx context.Context, fingerprint string, options *LookupOptions) ([]*LookupResult, error) {
	// Find machines with this fingerprint
	machines, _, err := ls.machineRepo.List(ctx, repositories.ListFilter{
		Filters: map[string]any{"fingerprint": fingerprint},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to find machines: %v", err)
	}

	// Get unique license IDs
	licenseIDs := make(map[string]bool)
	for _, machine := range machines {
		licenseIDs[machine.LicenseID] = true
	}

	// Lookup each license
	results := make([]*LookupResult, 0, len(licenseIDs))
	for licenseID := range licenseIDs {
		// Get license by ID
		licenseUUID, err := uuid.Parse(licenseID)
		if err != nil {
			continue
		}

		license, err := ls.licenseRepo.GetByID(ctx, licenseUUID)
		if err != nil {
			continue
		}

		// Create lookup result
		result := &LookupResult{
			Found:     true,
			License:   license,
			Timestamp: time.Now(),
		}

		// Validate status if requested
		if options.ValidateStatus && !ls.isLicenseValid(license) {
			result.Error = "license is not valid"
		}

		// Add policy if requested
		if options.IncludePolicy {
			policy, err := ls.loadPolicy(ctx, license.PolicyID)
			if err == nil {
				result.Policy = policy
			}
		}

		// Add machines if requested
		if options.IncludeMachines {
			machines, err := ls.loadMachines(ctx, license.ID)
			if err == nil {
				result.Machines = machines
			}
		}

		// Add users if requested
		if options.IncludeUsers {
			users, err := ls.loadUsers(ctx, license.ID)
			if err == nil {
				result.Users = users
			}
		}

		results = append(results, result)
	}

	return results, nil
}

// LookupByUser finds licenses by user ID (Ruby: lookup by user)
func (ls *LookupService) LookupByUser(ctx context.Context, userID string, options *LookupOptions) ([]*LookupResult, error) {
	// Find licenses owned by this user
	licenses, _, err := ls.licenseRepo.List(ctx, repositories.ListFilter{
		Filters: map[string]any{"user_id": userID},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to find licenses: %v", err)
	}

	// Create lookup results
	results := make([]*LookupResult, 0, len(licenses))
	for _, license := range licenses {
		result := &LookupResult{
			Found:     true,
			License:   license,
			Timestamp: time.Now(),
		}

		// Validate status if requested
		if options.ValidateStatus && !ls.isLicenseValid(license) {
			result.Error = "license is not valid"
		}

		// Add policy if requested
		if options.IncludePolicy {
			policy, err := ls.loadPolicy(ctx, license.PolicyID)
			if err == nil {
				result.Policy = policy
			}
		}

		// Add machines if requested
		if options.IncludeMachines {
			machines, err := ls.loadMachines(ctx, license.ID)
			if err == nil {
				result.Machines = machines
			}
		}

		// Add users if requested
		if options.IncludeUsers {
			users, err := ls.loadUsers(ctx, license.ID)
			if err == nil {
				result.Users = users
			}
		}

		results = append(results, result)
	}

	return results, nil
}

// LookupByStatus finds licenses by status (Ruby: with_status scope)
func (ls *LookupService) LookupByStatus(ctx context.Context, status string, options *LookupOptions) ([]*LookupResult, error) {
	// Convert status to filter
	var filters map[string]any

	switch strings.ToUpper(status) {
	case "SUSPENDED":
		filters = map[string]any{"suspended": true}
	case "ACTIVE":
		// Active licenses: not suspended, not expired, not expiring, not inactive
		// This is complex filtering that might need custom repository method
		filters = map[string]any{"suspended": false}
	case "EXPIRED":
		// Expired licenses: expiry < now
		filters = map[string]any{"expired": true}
	case "EXPIRING":
		// Expiring licenses: expiry between now and 3 days from now
		filters = map[string]any{"expiring": true}
	case "INACTIVE":
		// Inactive licenses: no recent activity
		filters = map[string]any{"inactive": true}
	case "BANNED":
		// Banned licenses: owner is banned
		filters = map[string]any{"banned": true}
	default:
		return nil, fmt.Errorf("unsupported status: %s", status)
	}

	// Search licenses with status filter
	licenses, _, err := ls.licenseRepo.List(ctx, repositories.ListFilter{
		Filters: filters,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to search licenses by status: %v", err)
	}

	// Create lookup results
	results := make([]*LookupResult, 0, len(licenses))
	for _, license := range licenses {
		result := &LookupResult{
			Found:     true,
			License:   license,
			Timestamp: time.Now(),
		}

		// Validate status if requested
		if options.ValidateStatus && !ls.isLicenseValid(license) {
			result.Error = "license is not valid"
		}

		// Add policy if requested
		if options.IncludePolicy {
			policy, err := ls.loadPolicy(ctx, license.PolicyID)
			if err == nil {
				result.Policy = policy
			}
		}

		// Add machines if requested
		if options.IncludeMachines {
			machines, err := ls.loadMachines(ctx, license.ID)
			if err == nil {
				result.Machines = machines
			}
		}

		// Add users if requested
		if options.IncludeUsers {
			users, err := ls.loadUsers(ctx, license.ID)
			if err == nil {
				result.Users = users
			}
		}

		results = append(results, result)
	}

	return results, nil
}

// === HELPER METHODS ===

// isLicenseValid checks if license is valid for lookup (Ruby: valid?)
// This is a simplified validation for lookup service - for full validation use ValidationService
func (ls *LookupService) isLicenseValid(license *entities.License) bool {
	// Check if license is suspended (Ruby: suspended?)
	if license.IsSuspended() {
		return false
	}

	// Check if license is banned (Ruby: banned?)
	// Ruby logic: return false if user_id.nil? || owner.nil?; owner.banned?
	if ls.isLicenseBanned(license) {
		return false
	}

	// Check expiry with policy context (Ruby: revoke_access? && expired?)
	if license.IsExpired() && ls.shouldRevokeAccessOnExpiry(license) {
		return false
	}

	return true
}

// isLicenseBanned checks if license is banned (Ruby: banned?)
// Simplified version for lookup service - doesn't require database access
func (ls *LookupService) isLicenseBanned(license *entities.License) bool {
	// Ruby logic: return false if user_id.nil? || owner.nil?
	if !license.HasUserID() {
		return false // No user ID means no owner to be banned
	}

	// For lookup service, we use a conservative approach:
	// If we can't easily determine banned status, assume not banned
	// Full validation should use ValidationService.isLicenseBanned() with database access
	return false
}

// shouldRevokeAccessOnExpiry checks if access should be revoked on expiry (Ruby: revoke_access?)
// Simplified version for lookup service - uses conservative default
func (ls *LookupService) shouldRevokeAccessOnExpiry(license *entities.License) bool {
	// Conservative default: revoke access on expiry
	// Full validation should load policy and use license.RevokeAccessWithPolicy(policy)
	return true
}

// loadPolicy loads policy for license (Ruby: load policy)
func (ls *LookupService) loadPolicy(ctx context.Context, policyID string) (*entities.Policy, error) {
	policyUUID, err := uuid.Parse(policyID)
	if err != nil {
		return nil, fmt.Errorf("invalid policy ID: %v", err)
	}

	policy, err := ls.policyRepo.GetByID(ctx, policyUUID)
	if err != nil {
		return nil, fmt.Errorf("policy not found: %v", err)
	}

	return policy, nil
}

// loadMachines loads machines for license (Ruby: load machines)
func (ls *LookupService) loadMachines(ctx context.Context, licenseID string) ([]*entities.Machine, error) {
	machines, _, err := ls.machineRepo.List(ctx, repositories.ListFilter{
		Filters: map[string]any{"license_id": licenseID},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to load machines: %v", err)
	}

	return machines, nil
}

// === SEARCH METHODS ===

// SearchLicenses searches licenses with filters (Ruby: search licenses)
func (ls *LookupService) SearchLicenses(ctx context.Context, filters map[string]any, options *LookupOptions) ([]*LookupResult, error) {
	// Search licenses with filters
	licenses, _, err := ls.licenseRepo.List(ctx, repositories.ListFilter{
		Filters: filters,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to search licenses: %v", err)
	}

	// Create lookup results
	results := make([]*LookupResult, 0, len(licenses))
	for _, license := range licenses {
		result := &LookupResult{
			Found:     true,
			License:   license,
			Timestamp: time.Now(),
		}

		// Validate status if requested
		if options.ValidateStatus && !ls.isLicenseValid(license) {
			result.Error = "license is not valid"
		}

		// Add policy if requested
		if options.IncludePolicy {
			policy, err := ls.loadPolicy(ctx, license.PolicyID)
			if err == nil {
				result.Policy = policy
			}
		}

		// Add machines if requested
		if options.IncludeMachines {
			machines, err := ls.loadMachines(ctx, license.ID)
			if err == nil {
				result.Machines = machines
			}
		}

		// Add users if requested
		if options.IncludeUsers {
			users, err := ls.loadUsers(ctx, license.ID)
			if err == nil {
				result.Users = users
			}
		}

		results = append(results, result)
	}

	return results, nil
}

// GetLicenseStats returns license statistics (Ruby: license stats)
func (ls *LookupService) GetLicenseStats(ctx context.Context, licenseKey string) (map[string]any, error) {
	// Find license
	license, err := ls.licenseRepo.GetByKey(ctx, licenseKey)
	if err != nil {
		return nil, fmt.Errorf("license not found: %v", err)
	}

	// Get policy
	policy, err := ls.loadPolicy(ctx, license.PolicyID)
	if err != nil {
		return nil, fmt.Errorf("failed to load policy: %v", err)
	}

	// Get machines
	machines, err := ls.loadMachines(ctx, license.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to load machines: %v", err)
	}

	// Calculate stats
	stats := map[string]any{
		"license_id":     license.ID,
		"license_key":    license.Key,
		"status":         license.Status(),
		"uses":           license.Uses,
		"machines_count": len(machines),
		"cores_count":    license.MachinesCoreCount,
		"suspended":      license.Suspended,
		"expired":        license.IsExpired(),
		"protected":      license.IsProtected(policy.Protected != nil && *policy.Protected),
		"max_machines":   license.GetMaxMachines(policy.MaxMachines),
		"max_uses":       license.GetMaxUses(policy.MaxUses),
		"max_cores":      license.GetMaxCores(policy.MaxCores),
		"max_processes":  license.GetMaxProcesses(policy.MaxProcesses),
		"max_users":      license.GetMaxUsers(policy.MaxUsers),
		"created_at":     license.CreatedAt,
		"updated_at":     license.UpdatedAt,
		"expires_at":     license.Expiry,
		"last_validated": license.LastValidatedAt,
		"last_check_in":  license.LastCheckInAt,
		"last_check_out": license.LastCheckOutAt,
	}

	return stats, nil
}

// loadUsers loads users for license (Ruby: load users/licensees)
func (ls *LookupService) loadUsers(ctx context.Context, licenseID string) ([]*entities.User, error) {
	// Get license users (licensees) - Ruby: license.licensees
	users, _, err := ls.userRepo.List(ctx, repositories.ListFilter{
		Filters: map[string]any{"license_id": licenseID},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to load license users: %v", err)
	}

	return users, nil
}
